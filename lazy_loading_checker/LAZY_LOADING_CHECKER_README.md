# Lazy Loading Checker

A Python script that analyzes web pages to check if images and iframes have proper lazy loading implementation.

## Features

- **Auto-detects input format**: Supports both TXT and CSV input files
- **Smart URL detection**: Automatically finds URL columns in CSV files
- **Metadata preservation**: Includes CSV metadata columns in output reports
- **Resumable processing**: Automatically resumes from where it left off if interrupted
- **Incremental writing**: Writes results to CSV after each URL check
- **Progress tracking**: Maintains progress log for large URL lists
- **Robust error handling**: Handles SSL errors, timeouts, and network issues gracefully
- **Automatic retries**: Built-in retry logic for temporary failures
- **Comprehensive analysis**: Checks all `<img>` and `<iframe>` tags on each page
- **Detailed reporting**: Identifies specific elements missing lazy loading
- **Summary statistics**: Provides overview of lazy loading implementation

## Installation

1. Install Python 3.6 or higher
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Input Formats

### TXT Format
One URL per line, comments start with `#`:
```
https://example.com/page1
https://example.com/page2
# This is a comment
```

### CSV Format
Auto-detects URL column and headers, supports additional metadata:

**With headers:**
```csv
url,page_type,priority,notes
https://example.com/,home,high,Main page
https://example.com/about,page,medium,About page
```

**Without headers (URLs in first column):**
```csv
https://example.com/,home,high,Main page
https://example.com/about,page,medium,About page
```

**Supported URL column names**: `url`, `URL`, `link`, `Link`, `website`, `Website`, `page`, `Page`, `address`, `Address`, `href`, `src`

**Smart header detection**: Automatically detects if the first row contains URLs (no headers) or column names (with headers)

## Usage

The script auto-detects the input format and supports various options:

```bash
# Basic usage
python lazy_loading_checker.py urls.txt
python lazy_loading_checker.py urls.csv

# Hierarchical sampling (recommended for large sites)
python lazy_loading_checker.py -sample urls.txt
python lazy_loading_checker.py --sample urls.csv

# Performance options for faster processing
python lazy_loading_checker.py -w 4 urls.csv                    # 4 parallel workers
python lazy_loading_checker.py -w 8 -d 0.1 urls.csv           # 8 workers, 0.1s delay
python lazy_loading_checker.py --sample -w 4 large_urls.csv    # Combined sampling + parallel

# Image list mode - extract unique images without lazy loading
python lazy_loading_checker.py --list-images urls.csv          # List all images without lazy loading
python lazy_loading_checker.py --list-images --sample -w 4 urls.csv  # Fast image extraction
```

### Hierarchical Sampling (`-sample` option)

The `-sample` option applies intelligent hierarchical directory sampling, checking only one URL per directory level. This is ideal for large sites where you want representative coverage without checking every page.

**How it works:**
- Analyzes URL path structure to determine hierarchy levels
- Selects one representative URL per directory level
- Maintains hierarchical coverage across the entire site

**Example:**
```
Input URLs:
  /destinations                    ← Level 1
  /destinations/usa               ← Level 2
  /destinations/usa/new_york      ← Level 3
  /destinations/usa/california    ← Level 3 (same as new_york)
  /destinations/canada            ← Level 2 (same as usa)

Selected URLs:
  /destinations                    ✓ Checked (Level 1)
  /destinations/usa               ✓ Checked (Level 2)
  /destinations/usa/new_york      ✓ Checked (Level 3)
  /destinations/usa/california    ✗ Skipped (same level as new_york)
  /destinations/canada            ✗ Skipped (same level as usa)
```

## Sample Usage

Use the provided sample files:
```bash
# TXT format
python lazy_loading_checker.py urls_sample.txt

# CSV format with headers
python lazy_loading_checker.py urls_sample.csv

# CSV format without headers
python lazy_loading_checker.py urls_no_headers.csv

# Hierarchical sampling
python lazy_loading_checker.py -sample urls_hierarchical_sample.txt
```

## CSV Report Columns

### Core Columns
- **url**: The URL that was checked
- **status**: 'success' or 'error'
- **source_line**: Line number from input file
- **source_format**: 'txt' or 'csv'
- **total_images**: Total number of images found
- **images_without_lazy**: Number of images missing lazy loading (excluding first)
- **total_iframes**: Total number of iframes found
- **iframes_without_lazy**: Number of iframes missing lazy loading (excluding first)
- **first_image_has_lazy**: Whether the first image has lazy loading (should be false)
- **first_iframe_has_lazy**: Whether the first iframe has lazy loading (context dependent)
- **images_missing_lazy**: List of specific images missing lazy loading
- **iframes_missing_lazy**: List of specific iframes missing lazy loading
- **error_message**: Error details if the check failed

### Metadata Columns (CSV Input Only)
- **meta_[column_name]**: Any additional columns from CSV input (e.g., `meta_page_type`, `meta_priority`)

## Expected Behavior

- **First image**: Should NOT have `loading="lazy"` (above the fold)
- **Subsequent images**: Should have `loading="lazy"` (below the fold)
- **First iframe**: May or may not have lazy loading (context dependent)
- **Subsequent iframes**: Should have `loading="lazy"`

## Example Output

### Regular Mode
```
Detected file format: CSV
Using column 'url' for URLs
Processing 10 URLs...
Checking 1/10: https://example.com/
Checking 2/10: https://example.com/about
...
```

### Sample Mode
```
Detected file format: TXT
Hierarchical sampling: 8 URLs selected from 35 total URLs
Sample URLs selected:
  https://example.com/ (levels: 1)
  https://example.com/destinations (levels: 2)
  https://example.com/destinations/usa (levels: 3)
  https://example.com/destinations/usa/new_york (levels: 4)
  https://example.com/holidays (levels: 2)
Processing 8 URLs...
Checking 1/8: https://example.com/
...

=== SUMMARY ===
URLs processed: 8
Successful checks: 8
Failed checks: 0

Images:
  Total images found: 32
  Images missing lazy loading: 2
  Images with proper lazy loading: 30

Iframes:
  Total iframes found: 3
  Iframes missing lazy loading: 0
  Iframes with proper lazy loading: 3

Detailed report saved to: lazy_loading_report_sample_20241205_143022.csv
```

## Advanced Features

### Image List Mode (`--list-images`)
Extract only unique image paths that don't have lazy loading, regardless of position:

```bash
# Extract all images without lazy loading
python lazy_loading_checker.py --list-images urls.csv

# Combined with sampling for faster processing
python lazy_loading_checker.py --list-images --sample -w 4 urls.csv
```

**Output**: Creates `images_without_lazy_YYYYMMDD_HHMMSS.csv` with unique image paths:
```csv
image_path
https://example.com/img/hero-banner.jpg
https://example.com/img/logo.png
https://example.com/uploads/featured-image.jpg
```

**Use cases:**
- **Audit existing sites**: Find all images that need lazy loading
- **Migration planning**: Identify images to update during site improvements
- **Performance optimization**: Focus on specific images for optimization
- **Quality assurance**: Verify lazy loading implementation across the site

## Performance Options

### Parallel Processing (`-w` / `--workers`)
Speed up processing by checking multiple URLs simultaneously:

```bash
# Use 4 parallel workers (recommended for most cases)
python lazy_loading_checker.py -w 4 urls.csv

# Use 8 workers for very large lists (be respectful to servers)
python lazy_loading_checker.py -w 8 urls.csv
```

**Performance gains:**
- **4 workers**: ~3-4x faster than single-threaded
- **8 workers**: ~6-7x faster than single-threaded
- **Diminishing returns**: Beyond 8 workers may not help much

### Reduced Delays (`-d` / `--delay`)
Reduce the delay between requests for faster processing:

```bash
# Reduce delay to 0.1 seconds (default is 0.5)
python lazy_loading_checker.py -d 0.1 urls.csv

# No delay (fastest, but be respectful to servers)
python lazy_loading_checker.py -d 0 urls.csv
```

**⚠️ Important**: Be respectful to servers. Start with default settings and only reduce delays if the server can handle it.

### Combined Optimizations
For maximum speed on large sites:

```bash
# Hierarchical sampling + parallel processing + reduced delay
python lazy_loading_checker.py --sample -w 4 -d 0.1 large_site_urls.csv

# This can process 7500+ URLs in minutes instead of hours
```

## Advanced Features

### Resumable Processing
The script automatically handles interruptions and can resume from where it left off:

- **Progress tracking**: Creates a `*_progress.json` file alongside the output CSV
- **Automatic resume**: Simply re-run the same command to continue from where you stopped
- **Incremental writing**: Results are written to CSV immediately after each check
- **Safe interruption**: Use `Ctrl+C` to safely stop and resume later

**Example:**
```bash
# Start processing
python lazy_loading_checker.py large_site_urls.csv

# If interrupted, simply run the same command again
python lazy_loading_checker.py large_site_urls.csv
# Output: "Resuming from previous run: 45/200 URLs already completed"
```

### CSV Delimiter Detection
Automatically detects common CSV delimiters:
- Comma (`,`)
- Semicolon (`;`)
- Tab (`\t`)

### Smart CSV Processing
- **Header detection**: Automatically detects if first row contains headers or data
- **URL column detection**: Smart detection using common naming patterns and content analysis
- **Flexible format support**: Works with or without column headers

### Robust Error Handling
The script handles various network and SSL issues:

- **SSL certificate errors**: Automatically retries with SSL verification disabled for self-signed certificates
- **Network timeouts**: Configurable timeout with retry logic
- **HTTP errors**: Retries on temporary server errors (429, 500, 502, 503, 504)
- **Connection issues**: Exponential backoff retry strategy
- **Graceful degradation**: Continues processing other URLs even if some fail

**Example SSL handling:**
```
Checking 5/10: https://example.com/secure-page
  SSL verification failed, retrying without verification...
```

### Metadata Preservation
All additional CSV columns are preserved in the output with `meta_` prefix, allowing you to:
- Track page types and priorities
- Include testing notes
- Maintain project metadata
- Cross-reference with other systems

## Files Created

The script creates several files during operation:

### Regular Mode
- **`lazy_loading_report_YYYYMMDD_HHMMSS.csv`**: Main results file (regular mode)
- **`lazy_loading_report_sample_YYYYMMDD_HHMMSS.csv`**: Main results file (sample mode)
- **`lazy_loading_report_*_progress.json`**: Progress tracking file

### Image List Mode
- **`images_without_lazy_YYYYMMDD_HHMMSS.csv`**: Unique image paths without lazy loading
- **`images_without_lazy_sample_YYYYMMDD_HHMMSS.csv`**: Image paths (sample mode)

### Progress Files
- Contains completion status, timestamps, and list of completed URLs
- Safe to delete after successful completion
- Used for resuming interrupted runs

## Error Handling

The script is designed to handle various real-world issues:

### SSL Certificate Issues
- **Self-signed certificates**: Automatically retries without SSL verification
- **Certificate chain problems**: Bypasses verification when needed
- **Mixed SSL environments**: Handles both secure and insecure sites

### Network Issues
- **Timeouts**: Configurable timeout with automatic retries
- **Server errors**: Retries on 429, 500, 502, 503, 504 status codes
- **Connection failures**: Exponential backoff retry strategy
- **DNS issues**: Graceful error reporting

### Processing Resilience
- **Individual failures**: One failed URL doesn't stop the entire process
- **Progress preservation**: Failed URLs are logged but processing continues
- **Detailed error reporting**: Specific error messages in CSV output

## Notes

- **Resumable**: The script can be safely interrupted and resumed
- **Incremental**: Results are written immediately, so partial results are always available
- **Respectful**: Adds small delays between requests to be respectful to servers
- **Flexible**: URLs without protocols are automatically prefixed with `https://`
- **Commented**: Lines starting with `#` in TXT files are treated as comments
- **Robust**: Handles SSL errors, network timeouts, and server issues gracefully
- **Metadata-aware**: CSV metadata is preserved and included in output reports
