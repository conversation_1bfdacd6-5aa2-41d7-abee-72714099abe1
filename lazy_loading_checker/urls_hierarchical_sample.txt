# Sample URLs showing hierarchical structure for testing -sample option

# Root level
https://bon-voyage.ddev.site/

# First level directories
https://bon-voyage.ddev.site/destinations
https://bon-voyage.ddev.site/holidays
https://bon-voyage.ddev.site/activities
https://bon-voyage.ddev.site/accommodations

# Second level - destinations
https://bon-voyage.ddev.site/destinations/usa
https://bon-voyage.ddev.site/destinations/canada
https://bon-voyage.ddev.site/destinations/europe

# Third level - USA destinations (should only pick first one)
https://bon-voyage.ddev.site/destinations/usa/new_york
https://bon-voyage.ddev.site/destinations/usa/california
https://bon-voyage.ddev.site/destinations/usa/florida
https://bon-voyage.ddev.site/destinations/usa/texas

# Fourth level - New York (should only pick first one)
https://bon-voyage.ddev.site/destinations/usa/new_york/manhattan
https://bon-voyage.ddev.site/destinations/usa/new_york/brooklyn
https://bon-voyage.ddev.site/destinations/usa/new_york/queens

# Third level - Canada destinations (should only pick first one)
https://bon-voyage.ddev.site/destinations/canada/ontario
https://bon-voyage.ddev.site/destinations/canada/british_columbia
https://bon-voyage.ddev.site/destinations/canada/quebec

# Second level - holidays (should only pick first one)
https://bon-voyage.ddev.site/holidays/family_holidays
https://bon-voyage.ddev.site/holidays/multi_centre_holidays
https://bon-voyage.ddev.site/holidays/fly_drive_holidays

# Second level - activities (should only pick first one)
https://bon-voyage.ddev.site/activities/helicopter_tours
https://bon-voyage.ddev.site/activities/city_tours
https://bon-voyage.ddev.site/activities/food_tours

# Deep nesting example
https://bon-voyage.ddev.site/destinations/usa/california/los_angeles/hollywood
https://bon-voyage.ddev.site/destinations/usa/california/san_francisco/fishermans_wharf
