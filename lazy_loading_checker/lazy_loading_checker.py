#!/usr/bin/env python3
"""
Lazy Loading Checker Script

This script takes a list of URLs and checks whether all <img> and <iframe> tags
(except the first instance) have loading="lazy" attributes.
Outputs results to a CSV file.

Supports both TXT and CSV input formats:
- TXT: One URL per line
- CSV: Auto-detects URL column, supports additional metadata columns

Usage:
    python lazy_loading_checker.py urls.txt
    python lazy_loading_checker.py urls.csv
"""

import requests
import csv
import sys
import os
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from datetime import datetime
import time
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def create_robust_session():
    """
    Create a requests session with robust error handling and retry logic.

    Returns:
        requests.Session: Configured session with retry logic
    """
    session = requests.Session()

    # Configure retry strategy
    retry_strategy = Retry(
        total=3,  # Total number of retries
        backoff_factor=1,  # Wait time between retries (1, 2, 4 seconds)
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry
        allowed_methods=["HEAD", "GET", "OPTIONS"]  # HTTP methods to retry
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Set headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    return session

def check_lazy_loading(url, timeout=10, session=None):
    """
    Check a single URL for proper lazy loading implementation.

    Args:
        url (str): The URL to check
        timeout (int): Request timeout in seconds
        session (requests.Session): Optional session to use for requests

    Returns:
        dict: Results dictionary with analysis
    """
    result = {
        'url': url,
        'status': 'error',
        'total_images': 0,
        'images_without_lazy': 0,
        'total_iframes': 0,
        'iframes_without_lazy': 0,
        'first_image_has_lazy': False,
        'first_iframe_has_lazy': False,
        'images_missing_lazy': [],
        'iframes_missing_lazy': [],
        'error_message': ''
    }

    # Use provided session or create a new one
    if session is None:
        session = create_robust_session()

    try:
        # Try with SSL verification first
        response = session.get(url, timeout=timeout, verify=True)
        response.raise_for_status()

    except requests.exceptions.SSLError as ssl_error:
        try:
            # Retry without SSL verification for self-signed certificates
            print(f"  SSL verification failed, retrying without verification...")
            response = session.get(url, timeout=timeout, verify=False)
            response.raise_for_status()
            result['error_message'] = f"SSL verification bypassed: {str(ssl_error)}"
        except Exception as retry_error:
            result['error_message'] = f"SSL error (retry failed): {str(retry_error)}"
            return result

    except requests.exceptions.RequestException as e:
        result['error_message'] = f"Request error: {str(e)}"
        return result
    except Exception as e:
        result['error_message'] = f"Unexpected error: {str(e)}"
        return result

    try:
        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Check images
        images = soup.find_all('img')
        result['total_images'] = len(images)

        if images:
            # Check first image
            first_img = images[0]
            result['first_image_has_lazy'] = first_img.get('loading') == 'lazy'

            # Check remaining images (should have lazy loading)
            for i, img in enumerate(images[1:], 1):
                if img.get('loading') != 'lazy':
                    result['images_without_lazy'] += 1
                    # Get image source for identification
                    src = img.get('src', img.get('data-src', 'no-src'))
                    result['images_missing_lazy'].append(f"Image {i+1}: {src}")

        # Check iframes
        iframes = soup.find_all('iframe')
        result['total_iframes'] = len(iframes)

        if iframes:
            # Check first iframe
            first_iframe = iframes[0]
            result['first_iframe_has_lazy'] = first_iframe.get('loading') == 'lazy'

            # Check remaining iframes (should have lazy loading)
            for i, iframe in enumerate(iframes[1:], 1):
                if iframe.get('loading') != 'lazy':
                    result['iframes_without_lazy'] += 1
                    # Get iframe source for identification
                    src = iframe.get('src', 'no-src')
                    result['iframes_missing_lazy'].append(f"Iframe {i+1}: {src}")

        result['status'] = 'success'

    except requests.exceptions.RequestException as e:
        result['error_message'] = f"Request error: {str(e)}"
    except Exception as e:
        result['error_message'] = f"Parsing error: {str(e)}"

    return result

def extract_images_without_lazy(url, timeout=10, session=None):
    """
    Extract all image paths that don't have lazy loading from a URL.

    Args:
        url (str): The URL to check
        timeout (int): Request timeout in seconds
        session (requests.Session): Optional session to use for requests

    Returns:
        dict: Results dictionary with image paths
    """
    result = {
        'url': url,
        'status': 'error',
        'images_without_lazy': [],
        'error_message': ''
    }

    # Use provided session or create a new one
    if session is None:
        session = create_robust_session()

    try:
        # Try with SSL verification first
        response = session.get(url, timeout=timeout, verify=True)
        response.raise_for_status()

    except requests.exceptions.SSLError as ssl_error:
        try:
            # Retry without SSL verification for self-signed certificates
            print(f"  SSL verification failed, retrying without verification...")
            response = session.get(url, timeout=timeout, verify=False)
            response.raise_for_status()
            result['error_message'] = f"SSL verification bypassed: {str(ssl_error)}"
        except Exception as retry_error:
            result['error_message'] = f"SSL error (retry failed): {str(retry_error)}"
            return result

    except requests.exceptions.HTTPError as http_error:
        # Handle 404s silently in image list mode - just return empty result
        if hasattr(http_error, 'response') and http_error.response.status_code == 404:
            result['status'] = 'success'  # Mark as success but with no images
            result['error_message'] = '404 Not Found (ignored)'
            return result
        else:
            result['error_message'] = f"HTTP error: {str(http_error)}"
            return result

    except requests.exceptions.RequestException as e:
        result['error_message'] = f"Request error: {str(e)}"
        return result
    except Exception as e:
        result['error_message'] = f"Unexpected error: {str(e)}"
        return result

    try:
        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Find all images
        images = soup.find_all('img')

        for img in images:
            if img.get('loading') != 'lazy':
                # Get image source
                src = img.get('src', img.get('data-src', ''))
                if src:
                    # Convert relative URLs to absolute
                    if src.startswith('/'):
                        from urllib.parse import urljoin
                        src = urljoin(url, src)
                    elif src.startswith('./') or not src.startswith(('http://', 'https://')):
                        from urllib.parse import urljoin
                        src = urljoin(url, src)

                    result['images_without_lazy'].append(src)

        result['status'] = 'success'

    except Exception as e:
        result['error_message'] = f"Parsing error: {str(e)}"

    return result

def detect_file_format(file_path):
    """
    Auto-detect whether the input file is TXT or CSV format.

    Args:
        file_path (str): Path to the input file

    Returns:
        str: 'txt' or 'csv'
    """
    # First check file extension
    _, ext = os.path.splitext(file_path.lower())
    if ext == '.csv':
        return 'csv'
    elif ext == '.txt':
        return 'txt'

    # If extension is ambiguous, peek at content
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()

            # Skip empty lines and comments
            while not first_line or first_line.startswith('#'):
                first_line = f.readline().strip()
                if not first_line:  # EOF
                    return 'txt'  # Default to txt if we can't determine

            # Check if it looks like CSV (has commas and doesn't look like a plain URL)
            if ',' in first_line and not first_line.startswith(('http://', 'https://', 'www.')):
                return 'csv'
            else:
                return 'txt'

    except Exception:
        return 'txt'  # Default to txt if we can't read the file

def find_url_column(csv_reader):
    """
    Find the column that contains URLs in a CSV file.

    Args:
        csv_reader: CSV DictReader object

    Returns:
        str: Column name containing URLs, or None if not found
    """
    # Common column names for URLs
    url_column_names = [
        'url', 'URL', 'link', 'Link', 'website', 'Website',
        'page', 'Page', 'address', 'Address', 'href', 'src'
    ]

    fieldnames = csv_reader.fieldnames
    if not fieldnames:
        return None

    # First, check for exact matches
    for col_name in url_column_names:
        if col_name in fieldnames:
            return col_name

    # Then check for partial matches
    for col_name in fieldnames:
        col_lower = col_name.lower()
        if any(url_term in col_lower for url_term in ['url', 'link', 'website', 'page']):
            return col_name

    # If no obvious URL column, use the first column
    return fieldnames[0]

def load_urls_from_txt(file_path):
    """
    Load URLs from a TXT file (one URL per line).

    Args:
        file_path (str): Path to the TXT file

    Returns:
        list: List of dictionaries with URL and metadata
    """
    urls_data = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    urls_data.append({
                        'url': line,
                        'source_line': line_num,
                        'source_format': 'txt'
                    })
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return []
    except Exception as e:
        print(f"Error reading TXT file: {e}")
        return []

    return urls_data

def is_url(text):
    """
    Check if a text string looks like a URL.

    Args:
        text (str): Text to check

    Returns:
        bool: True if text looks like a URL
    """
    if not text or not isinstance(text, str):
        return False

    text = text.strip().lower()
    return (text.startswith(('http://', 'https://', 'www.')) or
            ('.' in text and not text.startswith('#')))

def has_csv_headers(file_path, delimiter=','):
    """
    Determine if a CSV file has headers by checking if the first row contains URLs.

    Args:
        file_path (str): Path to the CSV file
        delimiter (str): CSV delimiter

    Returns:
        bool: True if file has headers, False if first row contains data
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if not first_line:
                return True  # Empty file, assume headers

            # Split by delimiter and check if any field looks like a URL
            fields = [field.strip().strip('"\'') for field in first_line.split(delimiter)]

            # If any field in the first row looks like a URL, it's probably data, not headers
            for field in fields:
                if is_url(field):
                    return False

            # If no URLs found in first row, it's probably headers
            return True

    except Exception:
        return True  # Default to assuming headers

def load_urls_from_csv(file_path):
    """
    Load URLs from a CSV file.

    Args:
        file_path (str): Path to the CSV file

    Returns:
        list: List of dictionaries with URL and metadata
    """
    urls_data = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Try to detect delimiter
            sample = f.read(1024)
            f.seek(0)

            delimiter = ','
            if sample.count(';') > sample.count(','):
                delimiter = ';'
            elif sample.count('\t') > sample.count(','):
                delimiter = '\t'

            # Check if file has headers
            has_headers = has_csv_headers(file_path, delimiter)

            if has_headers:
                # File has headers - use DictReader
                reader = csv.DictReader(f, delimiter=delimiter)
                url_column = find_url_column(reader)

                if not url_column:
                    print("Error: Could not find URL column in CSV file.")
                    return []

                print(f"Using column '{url_column}' for URLs (with headers)")

                for row_num, row in enumerate(reader, 2):  # Start at 2 (header is row 1)
                    url = row.get(url_column, '').strip()
                    if url and not url.startswith('#'):
                        # Include all CSV columns as metadata
                        url_data = {
                            'url': url,
                            'source_line': row_num,
                            'source_format': 'csv',
                            'url_column': url_column
                        }

                        # Add other columns as metadata
                        for key, value in row.items():
                            if key != url_column:
                                url_data[f'meta_{key}'] = value

                        urls_data.append(url_data)
            else:
                # File has no headers - treat as simple CSV with URLs in first column
                print("No headers detected - treating first column as URLs")
                reader = csv.reader(f, delimiter=delimiter)

                for row_num, row in enumerate(reader, 1):
                    if row and len(row) > 0:
                        url = row[0].strip()
                        if url and not url.startswith('#'):
                            url_data = {
                                'url': url,
                                'source_line': row_num,
                                'source_format': 'csv',
                                'url_column': 'column_0'
                            }

                            # Add additional columns as metadata if they exist
                            for i, value in enumerate(row[1:], 1):
                                if value.strip():
                                    url_data[f'meta_column_{i}'] = value.strip()

                            urls_data.append(url_data)

    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []

    return urls_data

def load_urls(file_path):
    """
    Load URLs from either TXT or CSV file, auto-detecting format.

    Args:
        file_path (str): Path to the input file

    Returns:
        list: List of dictionaries with URL and metadata
    """
    file_format = detect_file_format(file_path)
    print(f"Detected file format: {file_format.upper()}")

    if file_format == 'csv':
        return load_urls_from_csv(file_path)
    else:
        return load_urls_from_txt(file_path)

def get_progress_file(output_file):
    """
    Get the progress file path for a given output file.

    Args:
        output_file (str): Output CSV file path

    Returns:
        str: Progress file path
    """
    base_name = os.path.splitext(output_file)[0]
    return f"{base_name}_progress.json"

def load_progress(progress_file):
    """
    Load progress from a progress file.

    Args:
        progress_file (str): Progress file path

    Returns:
        dict: Progress data or empty dict if no progress file
    """
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load progress file: {e}")
    return {}

def save_progress(progress_file, progress_data):
    """
    Save progress to a progress file.

    Args:
        progress_file (str): Progress file path
        progress_data (dict): Progress data to save
    """
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2)
    except Exception as e:
        print(f"Warning: Could not save progress: {e}")

def initialize_csv_file(output_file, fieldnames):
    """
    Initialize CSV file with headers if it doesn't exist.

    Args:
        output_file (str): Output CSV file path
        fieldnames (list): List of column names
    """
    if not os.path.exists(output_file):
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

def append_result_to_csv(output_file, result, fieldnames):
    """
    Append a single result to the CSV file.

    Args:
        output_file (str): Output CSV file path
        result (dict): Result dictionary to append
        fieldnames (list): List of column names
    """
    try:
        # Convert lists to strings for CSV
        result_copy = result.copy()
        result_copy['images_missing_lazy'] = '; '.join(result.get('images_missing_lazy', []))
        result_copy['iframes_missing_lazy'] = '; '.join(result.get('iframes_missing_lazy', []))

        # Ensure all fieldnames are present
        for field in fieldnames:
            if field not in result_copy:
                result_copy[field] = ''

        with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writerow(result_copy)
    except Exception as e:
        print(f"Warning: Could not write result to CSV: {e}")

def get_completed_urls(progress_data):
    """
    Get set of URLs that have already been completed.

    Args:
        progress_data (dict): Progress data

    Returns:
        set: Set of completed URLs
    """
    return set(progress_data.get('completed_urls', []))

def get_url_path_hierarchy(url):
    """
    Extract the path hierarchy from a URL.

    Args:
        url (str): The URL to analyze

    Returns:
        list: List of path segments from root to leaf
    """
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        path = parsed.path.strip('/')

        if not path:
            return ['/']

        # Split path into segments and build hierarchy
        segments = path.split('/')
        hierarchy = []

        # Add root
        hierarchy.append('/')

        # Add each level of the hierarchy
        current_path = ''
        for segment in segments:
            if segment:  # Skip empty segments
                current_path += '/' + segment
                hierarchy.append(current_path)

        return hierarchy

    except Exception:
        return ['/']

def apply_hierarchical_sampling(urls_data):
    """
    Apply hierarchical directory sampling to URLs.
    Only keeps one URL per directory level in the hierarchy.

    Args:
        urls_data (list): List of URL data dictionaries

    Returns:
        list: Filtered list with one URL per directory level
    """
    if not urls_data:
        return []

    # Group URLs by their deepest path (directory level)
    path_groups = {}

    for url_info in urls_data:
        url = url_info['url']
        hierarchy = get_url_path_hierarchy(url)

        # Count the depth (number of path segments)
        depth = len(hierarchy) - 1  # Subtract 1 because root '/' is always included

        # Create a key that represents the directory level
        # For /activities/activity1 and /activities/activity2, both should have:
        # - depth: 2 (root -> activities -> specific_activity)
        # - parent: /activities
        parent_path = hierarchy[-2] if len(hierarchy) > 1 else '/'
        group_key = f"depth_{depth}:parent_{parent_path}"

        if group_key not in path_groups:
            path_groups[group_key] = []

        path_groups[group_key].append(url_info)

    # Select one URL from each group (the first one encountered)
    sampled_urls = []
    for group_key, urls_in_group in path_groups.items():
        # Take the first URL from each group
        sampled_urls.append(urls_in_group[0])

        # Debug info: show what we're skipping
        if len(urls_in_group) > 1:
            print(f"  Group {group_key}: Selected {urls_in_group[0]['url']}")
            print(f"    Skipped {len(urls_in_group) - 1} similar URLs at same level")

    return sampled_urls

def process_url_batch(url_infos, session, start_index):
    """
    Process a batch of URLs and return results.

    Args:
        url_infos (list): List of URL info dictionaries
        session: HTTP session to use
        start_index (int): Starting index for progress display

    Returns:
        list: List of results
    """
    results = []

    for i, url_info in enumerate(url_infos):
        url = url_info['url']

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        result = check_lazy_loading(url, session=session)

        # Add source metadata to result
        result['source_line'] = url_info.get('source_line', start_index + i)
        result['source_format'] = url_info.get('source_format', 'unknown')

        # Add any CSV metadata columns
        for key, value in url_info.items():
            if key.startswith('meta_'):
                result[key] = value

        results.append(result)

    return results

def process_single_url(url_info, session, index, total_urls):
    """
    Process a single URL and return the result.

    Args:
        url_info (dict): URL information dictionary
        session: HTTP session to use
        index (int): Current index for progress display
        total_urls (int): Total number of URLs

    Returns:
        dict: Result dictionary
    """
    url = url_info['url']
    print(f"Checking {index}/{total_urls}: {url}")

    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url

    result = check_lazy_loading(url, session=session)

    # Add source metadata to result
    result['source_line'] = url_info.get('source_line', index)
    result['source_format'] = url_info.get('source_format', 'unknown')

    # Add any CSV metadata columns
    for key, value in url_info.items():
        if key.startswith('meta_'):
            result[key] = value

    return result

def process_urls_for_image_list(urls_file, output_file, sample_mode=False, max_workers=1, delay=0.5):
    """
    Process URLs to extract unique image paths without lazy loading.

    Args:
        urls_file (str): Path to file containing URLs
        output_file (str): Path to output CSV file
        sample_mode (bool): If True, apply hierarchical directory sampling
        max_workers (int): Number of parallel workers
        delay (float): Delay between requests

    Returns:
        set: Set of unique image paths without lazy loading
    """
    # Load URLs using auto-detection
    urls_data = load_urls(urls_file)

    if not urls_data:
        return set()

    # Apply hierarchical sampling if requested
    original_count = len(urls_data)
    if sample_mode:
        urls_data = apply_hierarchical_sampling(urls_data)
        print(f"Hierarchical sampling: {len(urls_data)} URLs selected from {original_count} total URLs")

    # Create a robust session for all requests
    session = create_robust_session()

    print(f"Extracting images without lazy loading from {len(urls_data)} URLs...")

    # Progress tracking
    progress_file = output_file.replace('.csv', '_progress.json')
    progress_data = load_image_progress(progress_file)

    all_images = set(progress_data['discovered_images'].keys())
    image_sources = progress_data['discovered_images']  # Dict mapping image_path -> source_url
    completed_urls = set(progress_data['completed_urls'])

    # Filter out already completed URLs
    remaining_urls = [url_info for url_info in urls_data if url_info['url'] not in completed_urls]

    if completed_urls:
        print(f"Resuming: {len(completed_urls)} URLs already processed, {len(remaining_urls)} remaining")

    # Initialize CSV file with header (or resume existing)
    if not completed_urls:
        initialize_images_csv_with_source(output_file)

    try:
        if max_workers > 1:
            # Parallel processing
            print(f"Using {max_workers} parallel workers")

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Create sessions for each worker
                sessions = [create_robust_session() for _ in range(max_workers)]

                # Submit all URLs for processing
                future_to_info = {}
                for i, url_info in enumerate(remaining_urls):
                    session = sessions[i % len(sessions)]  # Round-robin session assignment
                    future = executor.submit(process_single_url_for_images, url_info, session, i + 1, len(remaining_urls))
                    future_to_info[future] = url_info

                # Process completed futures as they finish
                for future in as_completed(future_to_info):
                    url_info = future_to_info[future]
                    try:
                        images = future.result()
                        new_images = images - all_images  # Find only new images
                        if new_images:
                            # Track source URL for each new image
                            new_image_sources = {img: url_info['url'] for img in new_images}
                            append_images_to_csv_with_source(new_image_sources, output_file)
                            all_images.update(new_images)
                            image_sources.update(new_image_sources)
                            print(f"  Found {len(new_images)} new unique images (total: {len(all_images)})")

                        # Update progress
                        completed_urls.add(url_info['url'])
                        save_image_progress(progress_file, list(completed_urls), image_sources, len(urls_data))

                        # Small delay to be respectful to servers (reduced for parallel processing)
                        if delay > 0:
                            time.sleep(delay / max_workers)

                    except Exception as e:
                        print(f"Error processing {url_info['url']}: {e}")
        else:
            # Sequential processing
            for i, url_info in enumerate(remaining_urls, 1):
                images = process_single_url_for_images(url_info, session, i, len(remaining_urls))
                new_images = images - all_images  # Find only new images
                if new_images:
                    # Track source URL for each new image
                    new_image_sources = {img: url_info['url'] for img in new_images}
                    append_images_to_csv_with_source(new_image_sources, output_file)
                    all_images.update(new_images)
                    image_sources.update(new_image_sources)
                    print(f"  Found {len(new_images)} new unique images (total: {len(all_images)})")

                # Update progress
                completed_urls.add(url_info['url'])
                save_image_progress(progress_file, list(completed_urls), image_sources, len(urls_data))

                # Small delay to be respectful to servers
                if delay > 0:
                    time.sleep(delay)

    except KeyboardInterrupt:
        print(f"\nInterrupted! Found {len(all_images)} unique images so far.")
        print(f"Results saved to: {output_file}")
        print(f"Progress saved to: {progress_file}")
    except Exception as e:
        print(f"Error during processing: {e}")

    print(f"Found {len(all_images)} unique images without lazy loading")
    print(f"Results saved to: {output_file}")
    print(f"Progress saved to: {progress_file}")

    return all_images

def process_single_url_for_images(url_info, session, index, total_urls):
    """
    Process a single URL to extract images without lazy loading.

    Args:
        url_info (dict): URL information dictionary
        session: HTTP session to use
        index (int): Current index for progress display
        total_urls (int): Total number of URLs

    Returns:
        set: Set of image paths without lazy loading
    """
    url = url_info['url']
    print(f"Extracting images {index}/{total_urls}: {url}")

    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url

    result = extract_images_without_lazy(url, session=session)

    if result['status'] == 'success':
        return set(result['images_without_lazy'])
    else:
        # Only print error messages for non-404 errors
        if '404 Not Found (ignored)' not in result['error_message']:
            print(f"  Error: {result['error_message']}")
        return set()

def write_images_csv(images, output_file):
    """
    Write unique image paths to a CSV file.

    Args:
        images (set): Set of unique image paths
        output_file (str): Output CSV file path
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['image_path'])  # Header

        # Sort images for consistent output
        for image_path in sorted(images):
            writer.writerow([image_path])

def initialize_images_csv(output_file):
    """
    Initialize CSV file with header for incremental image writing.

    Args:
        output_file (str): Output CSV file path
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['image_path'])  # Header

def initialize_images_csv_with_source(output_file):
    """
    Initialize CSV file with header including source URL column.

    Args:
        output_file (str): Output CSV file path
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['image_path', 'source_url'])  # Header with source

def append_images_to_csv(images, output_file):
    """
    Append new unique image paths to an existing CSV file.

    Args:
        images (set): Set of new unique image paths to append
        output_file (str): Output CSV file path
    """
    with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # Sort images for consistent output
        for image_path in sorted(images):
            writer.writerow([image_path])

def append_images_to_csv_with_source(image_sources, output_file):
    """
    Append new unique image paths with their source URLs to CSV file.

    Args:
        image_sources (dict): Dict mapping image_path -> source_url
        output_file (str): Output CSV file path
    """
    with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # Sort images for consistent output
        for image_path in sorted(image_sources.keys()):
            writer.writerow([image_path, image_sources[image_path]])

def load_image_progress(progress_file):
    """
    Load progress data for image list mode.

    Args:
        progress_file (str): Path to progress JSON file

    Returns:
        dict: Progress data with completed URLs and discovered images
    """
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass

    # Return default structure
    return {
        'completed_urls': [],
        'discovered_images': {},  # image_path -> source_url mapping
        'total_urls': 0,
        'start_time': datetime.now().isoformat(),
        'last_updated': datetime.now().isoformat()
    }

def save_image_progress(progress_file, completed_urls, discovered_images, total_urls):
    """
    Save progress data for image list mode.

    Args:
        progress_file (str): Path to progress JSON file
        completed_urls (list): List of completed URL strings
        discovered_images (dict): Dict mapping image_path -> source_url
        total_urls (int): Total number of URLs to process
    """
    progress_data = {
        'completed_urls': completed_urls,
        'discovered_images': discovered_images,
        'total_urls': total_urls,
        'start_time': load_image_progress(progress_file).get('start_time', datetime.now().isoformat()),
        'last_updated': datetime.now().isoformat()
    }

    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
    except IOError as e:
        print(f"Warning: Could not save progress file: {e}")

def load_results_from_csv(csv_file):
    """
    Load existing results from a CSV file.

    Args:
        csv_file (str): Path to CSV file

    Returns:
        list: List of result dictionaries
    """
    results = []
    if not os.path.exists(csv_file):
        return results

    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Convert string lists back to lists
                if 'images_missing_lazy' in row:
                    row['images_missing_lazy'] = [x.strip() for x in row['images_missing_lazy'].split(';') if x.strip()]
                if 'iframes_missing_lazy' in row:
                    row['iframes_missing_lazy'] = [x.strip() for x in row['iframes_missing_lazy'].split(';') if x.strip()]

                # Convert numeric fields
                for field in ['total_images', 'images_without_lazy', 'total_iframes', 'iframes_without_lazy', 'source_line']:
                    if field in row and row[field]:
                        try:
                            row[field] = int(row[field])
                        except ValueError:
                            row[field] = 0

                # Convert boolean fields
                for field in ['first_image_has_lazy', 'first_iframe_has_lazy']:
                    if field in row:
                        row[field] = row[field].lower() in ('true', '1', 'yes')

                results.append(row)
    except Exception as e:
        print(f"Warning: Could not load existing results: {e}")

    return results

def process_urls(urls_file, output_file, sample_mode=False, max_workers=1, delay=0.5):
    """
    Process a list of URLs from a file (TXT or CSV format) with resumable functionality.

    Args:
        urls_file (str): Path to file containing URLs
        output_file (str): Path to output CSV file
        sample_mode (bool): If True, apply hierarchical directory sampling

    Returns:
        list: List of result dictionaries
    """
    results = []

    # Load URLs using auto-detection
    urls_data = load_urls(urls_file)

    if not urls_data:
        return []

    # Apply hierarchical sampling if requested
    original_count = len(urls_data)
    if sample_mode:
        urls_data = apply_hierarchical_sampling(urls_data)
        print(f"Hierarchical sampling: {len(urls_data)} URLs selected from {original_count} total URLs")

        # Show some examples of the sampling
        if len(urls_data) > 0:
            print("Sample URLs selected:")
            for i, url_info in enumerate(urls_data[:5]):  # Show first 5
                hierarchy = get_url_path_hierarchy(url_info['url'])
                print(f"  {url_info['url']} (levels: {len(hierarchy)})")
            if len(urls_data) > 5:
                print(f"  ... and {len(urls_data) - 5} more")

    # Set up progress tracking
    progress_file = get_progress_file(output_file)
    progress_data = load_progress(progress_file)
    completed_urls = get_completed_urls(progress_data)

    # Determine fieldnames for CSV (need to do this early for incremental writing)
    base_fieldnames = [
        'url', 'status', 'source_line', 'source_format',
        'total_images', 'images_without_lazy', 'total_iframes', 'iframes_without_lazy',
        'first_image_has_lazy', 'first_iframe_has_lazy',
        'images_missing_lazy', 'iframes_missing_lazy', 'error_message'
    ]

    # Find any additional metadata columns from CSV input
    all_keys = set()
    for url_info in urls_data:
        all_keys.update(url_info.keys())
    meta_columns = sorted([key for key in all_keys if key.startswith('meta_')])
    fieldnames = base_fieldnames + meta_columns

    # Initialize CSV file with headers
    initialize_csv_file(output_file, fieldnames)

    # Create a robust session for all requests
    session = create_robust_session()

    # Filter out already completed URLs
    remaining_urls = [url_info for url_info in urls_data if url_info['url'] not in completed_urls]

    total_urls = len(urls_data)
    completed_count = len(completed_urls)
    remaining_count = len(remaining_urls)

    if completed_count > 0:
        print(f"Resuming from previous run: {completed_count}/{total_urls} URLs already completed")

    if remaining_count == 0:
        print("All URLs have already been processed!")
        # Load existing results for summary
        return load_results_from_csv(output_file)

    print(f"Processing {remaining_count} remaining URLs...")

    # Initialize progress data if needed
    if 'start_time' not in progress_data:
        progress_data['start_time'] = datetime.now().isoformat()
        progress_data['input_file'] = urls_file
        progress_data['output_file'] = output_file
        progress_data['total_urls'] = total_urls
        progress_data['completed_urls'] = list(completed_urls)

    try:
        if max_workers > 1:
            # Parallel processing
            print(f"Using {max_workers} parallel workers")

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Create sessions for each worker
                sessions = [create_robust_session() for _ in range(max_workers)]

                # Submit all URLs for processing
                future_to_info = {}
                for i, url_info in enumerate(remaining_urls):
                    session = sessions[i % len(sessions)]  # Round-robin session assignment
                    future = executor.submit(process_single_url, url_info, session, completed_count + i + 1, total_urls)
                    future_to_info[future] = url_info

                # Process completed futures as they finish
                for future in as_completed(future_to_info):
                    url_info = future_to_info[future]
                    try:
                        result = future.result()

                        # Write result immediately to CSV
                        append_result_to_csv(output_file, result, fieldnames)
                        results.append(result)

                        # Update progress
                        progress_data['completed_urls'].append(url_info['url'])
                        progress_data['last_completed'] = datetime.now().isoformat()
                        progress_data['completed_count'] = len(progress_data['completed_urls'])
                        save_progress(progress_file, progress_data)

                        # Small delay to be respectful to servers (reduced for parallel processing)
                        if delay > 0:
                            time.sleep(delay / max_workers)

                    except Exception as e:
                        print(f"Error processing {url_info['url']}: {e}")
        else:
            # Sequential processing (original logic)
            for i, url_info in enumerate(remaining_urls, completed_count + 1):
                result = process_single_url(url_info, session, i, total_urls)

                # Write result immediately to CSV
                append_result_to_csv(output_file, result, fieldnames)
                results.append(result)

                # Update progress
                progress_data['completed_urls'].append(url_info['url'])
                progress_data['last_completed'] = datetime.now().isoformat()
                progress_data['completed_count'] = len(progress_data['completed_urls'])
                save_progress(progress_file, progress_data)

                # Small delay to be respectful to servers
                if delay > 0:
                    time.sleep(delay)

    except KeyboardInterrupt:
        print(f"\nInterrupted! Progress saved. Resume by running the same command.")
        print(f"Completed: {len(progress_data['completed_urls'])}/{total_urls} URLs")
        return results
    except Exception as e:
        print(f"Error during processing: {e}")
        print(f"Progress saved. Completed: {len(progress_data['completed_urls'])}/{total_urls} URLs")
        return results

    # Mark as completed
    progress_data['completed'] = True
    progress_data['end_time'] = datetime.now().isoformat()
    save_progress(progress_file, progress_data)

    print(f"Processing completed! Results written to: {output_file}")

    return results

def print_summary(results):
    """
    Print a summary of the results.

    Args:
        results (list): List of result dictionaries
    """
    total_urls = len(results)
    successful_checks = len([r for r in results if r['status'] == 'success'])

    total_images = sum(r['total_images'] for r in results)
    total_images_without_lazy = sum(r['images_without_lazy'] for r in results)

    total_iframes = sum(r['total_iframes'] for r in results)
    total_iframes_without_lazy = sum(r['iframes_without_lazy'] for r in results)

    print(f"\n=== SUMMARY ===")
    print(f"URLs processed: {total_urls}")
    print(f"Successful checks: {successful_checks}")
    print(f"Failed checks: {total_urls - successful_checks}")
    print(f"\nImages:")
    print(f"  Total images found: {total_images}")
    print(f"  Images missing lazy loading: {total_images_without_lazy}")
    print(f"  Images with proper lazy loading: {total_images - total_images_without_lazy}")
    print(f"\nIframes:")
    print(f"  Total iframes found: {total_iframes}")
    print(f"  Iframes missing lazy loading: {total_iframes_without_lazy}")
    print(f"  Iframes with proper lazy loading: {total_iframes - total_iframes_without_lazy}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description='Check lazy loading implementation on web pages',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python lazy_loading_checker.py urls.txt
  python lazy_loading_checker.py urls.csv
  python lazy_loading_checker.py -sample urls.txt
  python lazy_loading_checker.py --sample large_site_urls.csv

The -sample option applies hierarchical directory sampling, checking only
one URL per directory level. For example:
  /destinations          ✓ (checked)
  /destinations/usa      ✓ (checked - different level)
  /destinations/usa/ny   ✓ (checked - different level)
  /destinations/usa/ca   ✗ (skipped - same level as ny)
        """
    )

    parser.add_argument('urls_file', help='Path to file containing URLs (TXT or CSV format)')
    parser.add_argument('-s', '--sample', action='store_true',
                       help='Apply hierarchical directory sampling (one URL per directory level)')
    parser.add_argument('-w', '--workers', type=int, default=1, metavar='N',
                       help='Number of parallel workers (default: 1, recommended: 4-8)')
    parser.add_argument('-d', '--delay', type=float, default=0.5, metavar='SECONDS',
                       help='Delay between requests in seconds (default: 0.5, use 0.1 for faster processing)')
    parser.add_argument('--timeout', type=int, default=10, metavar='SECONDS',
                       help='Request timeout in seconds (default: 10)')
    parser.add_argument('--list-images', action='store_true',
                       help='Only compile a list of unique image paths without lazy loading (outputs images_without_lazy_TIMESTAMP.csv)')

    args = parser.parse_args()

    if not os.path.exists(args.urls_file):
        print(f"Error: File '{args.urls_file}' does not exist.")
        sys.exit(1)

    # Generate output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if args.list_images:
        # Image list mode
        sample_suffix = "_sample" if args.sample else ""
        output_file = f"images_without_lazy{sample_suffix}_{timestamp}.csv"

        # Process URLs to extract images without lazy loading
        images = process_urls_for_image_list(
            args.urls_file,
            output_file,
            sample_mode=args.sample,
            max_workers=args.workers,
            delay=args.delay
        )

        if not images:
            print("No images without lazy loading found.")

    else:
        # Regular lazy loading analysis mode
        sample_suffix = "_sample" if args.sample else ""
        output_file = f"lazy_loading_report{sample_suffix}_{timestamp}.csv"

        # Process URLs (with incremental writing and resumable functionality)
        results = process_urls(
            args.urls_file,
            output_file,
            sample_mode=args.sample,
            max_workers=args.workers,
            delay=args.delay
        )

        if not results:
            print("No URLs to process.")
            sys.exit(1)

        # Print summary
        print_summary(results)

        print(f"\nDetailed report saved to: {output_file}")

if __name__ == "__main__":
    main()
