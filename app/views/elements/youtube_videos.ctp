<?php if (empty($youtubeVideos['items'])) : ?>
    <p>Sorry, there are no matching videos</p>
<?php else : ?>
    <div class="youtube-videos">
        <?php foreach ($youtubeVideos['items'] as $v): ?>
            <div class="youtube-videos__video">
                <div class="youtube-videos__iframe-wrapper">
                    <?php
                    echo $image->video($v['contentDetails']['videoId'], array(
                        'class' => 'youtube-videos__iframe',
                        'loading' => 'lazy'  // YouTube videos in galleries are likely below the fold
                    ));
                    ?>
                </div>

                <p class="youtube-videos__link">
                    <a href="https://www.youtube.com/watch?v=<?php echo $v['contentDetails']['videoId']; ?>">
                        <?php echo $v['snippet']['title']; ?>
                    </a>
                </p>
            </div>
        <?php endforeach ?>
    </div>
<?php endif; ?>
