<?php

$options = array(
    'destinations' => array(
        'class'        => 'destinations',
        'heading'      => 'Holiday ideas',
        'model'        => 'HolidayType',
        'displayField' => 'name',
        'controller'   => 'holiday_types',
        'slugParam'    => 'holiday_type_slug',
        'slugField'    => 'slug',
        'otherSection' => 'holidays',
        'imageModel'   => 'Image',
    ),
    'holidays' => array(
        'class'        => 'holidays',
        'heading'      => 'Related Destinations',
        'model'        => 'Destination',
        'displayField' => 'name',
        'controller'   => 'destinations',
        'slugParam'    => 'destination_slug',
        'slugField'    => 'slug',
        'otherSection' => 'destinations',
        'imageModel'   => 'MainImage',
    ),
);

if ($section == 'landing_pages') {
    $section = 'destinations';
}

$options = $options[$section];
$relatedImage = false;

foreach ($related as $r) {
    $img = $r[$options['imageModel']];

    if (!$relatedImage && $img && $img['id']) {
        $relatedImage = array('Image' => $img);
        $link = Router::url(array(
            'controller'          => $options['controller'],
            'action'              => 'view',
            $options['slugParam'] => $r[$options['model']][$options['slugField']],
            'section'             => $options['otherSection']
        ));
        break;
    }
}

?>

<section class="related-module related-module--hols-dest related-module--<?php echo $options['class']; ?>">
    <h3><?php echo $options['heading']; ?></h3>

    <?php if (!empty($relatedImage['Image']['id'])): ?>
        <div class="related-module__image">
            <?php
                echo $html->link(
                    $image->image($relatedImage, array(
                        'version' => 'fit226x0',
                        'noLightbox' => true,
                        'loading' => 'lazy'  // Related images in sidebar are typically below the fold
                    )),
                    $link,
                    array('escape' => false)
                );
            ?>
        </div>
    <?php endif ?>

    <ul>
        <?php foreach ($related as $i => $item) :?>
            <li>
                <?php
                    echo $html->link(
                        $item[$options['model']][$options['displayField']],
                        array(
                            'controller'          => $options['controller'],
                            'action'              => 'view',
                            $options['slugParam'] => $item[$options['model']][$options['slugField']],
                            'section'             => $options['otherSection']
                        )
                    );
                ?>
            </li>

            <?php
                if (!$relatedImage && $r[$options['imageModel']['id']]) {
                    $relatedImage = array('Image' => $item[$options['imageModel']]);
                }
            ?>
        <?php endforeach; ?>
    </ul>
</section>
