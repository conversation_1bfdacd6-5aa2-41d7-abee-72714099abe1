<?php
// Debug CSV parsing
$csvFile = $argv[1];

if (!file_exists($csvFile)) {
    die("CSV file not found: $csvFile\n");
}

$handle = fopen($csvFile, 'r');
if (!$handle) {
    die("Could not open CSV file: $csvFile\n");
}

echo "Reading CSV file: $csvFile\n";

// Read header
$header = fgetcsv($handle);
echo "Header found: ";
var_dump($header);

echo "Looking for 'destination_id': " . (in_array('destination_id', $header) ? 'FOUND' : 'NOT FOUND') . "\n";
echo "Looking for 'new_meta_title': " . (in_array('new_meta_title', $header) ? 'FOUND' : 'NOT FOUND') . "\n";

// Read first data row
$data = fgetcsv($handle);
echo "First data row: ";
var_dump($data);

fclose($handle);
?>
