<?php
/**
 * PRODUCTION Meta Title Updater
 *
 * Clean, production-ready script for updating destination meta titles
 *
 * Usage:
 *   php production_meta_title_updater.php your_file.csv           (live update)
 *   php production_meta_title_updater.php your_file.csv --dry-run (test mode)
 *
 * CSV Format: destination_id,new_meta_title
 */

class ProductionMetaTitleUpdater {

    // PRODUCTION DATABASE CONFIGURATION
    // Update these constants for your production environment
    const DB_HOSTNAME = 'your-production-host';
    const DB_USERNAME = 'your-production-username';
    const DB_PASSWORD = 'your-production-password';
    const DB_DATABASE = 'your-production-database';

    private $mysqli;
    private $updated_count = 0;
    private $error_count = 0;
    private $errors = array();
    private $dry_run = false;

    public function __construct($dry_run = false) {
        $this->dry_run = $dry_run;
        $this->connectDatabase();
    }

    private function connectDatabase() {
        $this->mysqli = new mysqli(self::DB_HOSTNAME, self::DB_USERNAME, self::DB_PASSWORD, self::DB_DATABASE);

        if (mysqli_connect_errno()) {
            die("❌ Database connection failed: " . mysqli_connect_error() . "\n");
        }

        echo "✅ Connected to production database successfully.\n";
    }

    public function updateFromCSV($csvFile) {
        if (!file_exists($csvFile)) {
            die("❌ CSV file not found: $csvFile\n");
        }

        echo "🔄 Processing CSV file: $csvFile\n";

        if ($this->dry_run) {
            echo "🧪 DRY RUN MODE - No changes will be made to the database\n";
        } else {
            echo "⚠️  PRODUCTION UPDATE - Please ensure you have a database backup!\n";
            echo "Press Enter to continue or Ctrl+C to cancel...";
            fgets(STDIN);
        }

        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            die("❌ Could not open CSV file: $csvFile\n");
        }

        // Read and validate header
        $header = fgetcsv($handle);
        if (!$header) {
            die("❌ Could not read CSV header\n");
        }

        // Remove BOM from first column if present
        if (!empty($header[0])) {
            $header[0] = preg_replace('/^\xEF\xBB\xBF/', '', $header[0]);
        }

        if (!in_array('destination_id', $header) || !in_array('new_meta_title', $header)) {
            echo "❌ CSV must have 'destination_id' and 'new_meta_title' columns\n";
            echo "Found columns: " . implode(', ', $header) . "\n";
            die();
        }

        $destination_id_index = array_search('destination_id', $header);
        $meta_title_index = array_search('new_meta_title', $header);

        if ($this->dry_run) {
            echo "🧪 Starting dry run analysis...\n\n";
        } else {
            echo "🚀 Starting production updates...\n\n";
        }

        $row_number = 1;
        while (($data = fgetcsv($handle)) !== false) {
            $row_number++;

            if (count($data) < max($destination_id_index, $meta_title_index) + 1) {
                $this->logError($row_number, "Insufficient columns in row");
                continue;
            }

            $destination_id = trim($data[$destination_id_index]);
            $new_meta_title = trim($data[$meta_title_index]);

            if (empty($destination_id) || !is_numeric($destination_id)) {
                $this->logError($row_number, "Invalid destination_id: '$destination_id'");
                continue;
            }

            if (empty($new_meta_title)) {
                $this->logError($row_number, "Empty meta_title for destination_id: $destination_id");
                continue;
            }

            $this->updateDestinationMetaTitle($destination_id, $new_meta_title, $row_number);
        }

        fclose($handle);
        $this->printSummary();
    }

    private function updateDestinationMetaTitle($destination_id, $new_meta_title, $row_number) {
        // Get current destination info
        $check_stmt = $this->mysqli->prepare("SELECT id, name, meta_title FROM destinations WHERE id = ?");
        $check_stmt->bind_param("i", $destination_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();

        if ($result->num_rows === 0) {
            $this->logError($row_number, "Destination with ID $destination_id not found");
            $check_stmt->close();
            return;
        }

        $destination = $result->fetch_assoc();
        $old_meta_title = $destination['meta_title'];
        $destination_name = $destination['name'];
        $check_stmt->close();

        // Update the meta title (or simulate in dry run)
        if ($this->dry_run) {
            // Dry run - just show what would be changed
            if ($old_meta_title !== $new_meta_title) {
                $this->updated_count++;
                echo "🧪 WOULD UPDATE destination $destination_id ($destination_name)\n";
                echo "   Current: '$old_meta_title'\n";
                echo "   New:     '$new_meta_title'\n\n";
            } else {
                echo "ℹ️  No change needed for destination $destination_id ($destination_name)\n\n";
            }
        } else {
            // Live update
            $update_stmt = $this->mysqli->prepare("UPDATE destinations SET meta_title = ?, modified = NOW() WHERE id = ?");
            $update_stmt->bind_param("si", $new_meta_title, $destination_id);

            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $this->updated_count++;
                    echo "✅ Updated destination $destination_id ($destination_name)\n";
                    echo "   Old: '$old_meta_title'\n";
                    echo "   New: '$new_meta_title'\n\n";
                } else {
                    echo "ℹ️  No change needed for destination $destination_id ($destination_name)\n\n";
                }
            } else {
                $this->logError($row_number, "Database error updating destination $destination_id: " . $this->mysqli->error);
            }

            $update_stmt->close();
        }
    }

    private function logError($row_number, $message) {
        $this->error_count++;
        $this->errors[] = "Row $row_number: $message";
        echo "❌ Error on row $row_number: $message\n";
    }

    private function printSummary() {
        echo "\n" . str_repeat("=", 60) . "\n";

        if ($this->dry_run) {
            echo "🧪 DRY RUN SUMMARY\n";
            echo str_repeat("=", 60) . "\n";
            echo "🔍 Would update: {$this->updated_count} destinations\n";
            echo "❌ Errors found: {$this->error_count}\n";

            if (!empty($this->errors)) {
                echo "\n📋 Error details:\n";
                foreach ($this->errors as $error) {
                    echo "   • $error\n";
                }
            }

            if ($this->updated_count > 0) {
                echo "\n✅ Dry run completed - ready for production!\n";
                echo "\n💡 To apply changes:\n";
                echo "   php production_meta_title_updater.php your_file.csv\n";
            } else {
                echo "\n⚠️  No changes would be made\n";
            }
        } else {
            echo "🎯 PRODUCTION UPDATE SUMMARY\n";
            echo str_repeat("=", 60) . "\n";
            echo "✅ Successfully updated: {$this->updated_count} destinations\n";
            echo "❌ Errors encountered: {$this->error_count}\n";

            if (!empty($this->errors)) {
                echo "\n📋 Error details:\n";
                foreach ($this->errors as $error) {
                    echo "   • $error\n";
                }
            }

            if ($this->updated_count > 0) {
                echo "\n🎉 Production update completed successfully!\n";
            }

            echo "\n💡 Next steps:\n";
            echo "   • Verify changes on your website\n";
            echo "   • Clear any caches if needed\n";
            echo "   • Test a few pages to confirm updates\n";
        }
    }

    public function __destruct() {
        if ($this->mysqli) {
            mysqli_close($this->mysqli);
        }
    }
}

// Main execution
if ($argc < 2) {
    echo "🚀 Production Meta Title Updater\n";
    echo "================================\n\n";
    echo "Usage:\n";
    echo "  php production_meta_title_updater.php <csv_file>           (live update)\n";
    echo "  php production_meta_title_updater.php <csv_file> --dry-run (test mode)\n\n";
    echo "CSV Format:\n";
    echo "destination_id,new_meta_title\n";
    echo "123,\"New Meta Title Here\"\n";
    echo "456,\"Another Meta Title\"\n\n";
    echo "⚠️  IMPORTANT: Always backup your database before running on production!\n";
    echo "💡 TIP: Use --dry-run first to test your changes safely\n";
    exit(1);
}

$csvFile = $argv[1];
$dry_run = isset($argv[2]) && $argv[2] === '--dry-run';

$updater = new ProductionMetaTitleUpdater($dry_run);
$updater->updateFromCSV($csvFile);
