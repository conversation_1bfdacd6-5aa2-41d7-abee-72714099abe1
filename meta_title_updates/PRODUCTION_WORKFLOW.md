# Production Meta Title Update Workflow

## 🚨 CRITICAL: Pre-Production Checklist

- [ ] **Database backup completed**
- [ ] **CSV file validated and tested on staging**
- [ ] **Production database credentials configured**
- [ ] **Maintenance window scheduled (if needed)**

## 📋 Step-by-Step Production Workflow

### 1. Prepare Your CSV File
Your CSV must have exactly these columns:
```csv
destination_id,new_meta_title
143,"Alabama Holidays & Tours 2025/26 | Bon Voyage Travel"
9,"Alaska Holidays & Tours 2025/26 | Bon Voyage Travel"
```

### 2. Configure Database Credentials
Edit `production_meta_title_updater.php` lines 13-16:
```php
const DB_HOSTNAME = 'your-production-host';
const DB_USERNAME = 'your-production-username';  
const DB_PASSWORD = 'your-production-password';
const DB_DATABASE = 'your-production-database';
```

### 3. Upload Files to Production Server
```bash
# Upload the script and CSV to your production server
scp production_meta_title_updater.php user@server:/path/to/site/
scp your_meta_titles.csv user@server:/path/to/site/
```

### 4. Test with Dry Run (RECOMMENDED)
```bash
# SSH to production server
ssh user@server

# Navigate to site directory
cd /path/to/site/

# Test first with dry run (no database changes)
php production_meta_title_updater.php your_meta_titles.csv --dry-run
```

### 5. Run the Live Update
```bash
# After dry run looks good, run the live update
php production_meta_title_updater.php your_meta_titles.csv
```

### 6. Verify the Updates
```bash
# Check a few updated destinations
mysql -u username -p -e "SELECT id, name, meta_title FROM destinations WHERE id IN (143, 9, 256) ORDER BY id;"

# Count total updated destinations
mysql -u username -p -e "SELECT COUNT(*) as updated_count FROM destinations WHERE meta_title LIKE '%2025/26%';"
```

### 7. Post-Update Tasks
- [ ] **Clear website caches** (if applicable)
- [ ] **Test a few destination pages** to confirm meta titles appear correctly
- [ ] **Check Google Search Console** for any crawl errors (after 24-48 hours)
- [ ] **Monitor site performance** for any issues

## 🔧 Alternative: DDEV Production Method

If using DDEV on production:
```bash
# Start DDEV
ddev start

# Test with dry run first
ddev exec php production_meta_title_updater.php your_meta_titles.csv --dry-run

# Run live update after dry run looks good
ddev exec php production_meta_title_updater.php your_meta_titles.csv
```

## 🚨 Emergency Rollback

If you need to rollback changes:
```bash
# Restore from your database backup
mysql -u username -p database_name < backup_before_meta_title_update.sql
```

## 📊 Expected Results

- **118 destinations updated** (based on your SEO audit)
- **New format**: "Location Holidays & Tours 2025/26 | Bon Voyage Travel"
- **Zero downtime** (updates are instant)
- **No cache clearing needed** (unless your site heavily caches database content)

## 🔍 Troubleshooting

### Common Issues:
1. **Database connection failed**: Check credentials and server access
2. **CSV format errors**: Ensure proper column headers and data format
3. **Permission denied**: Check file permissions and database user privileges
4. **Timeout on large updates**: Consider splitting CSV into smaller batches

### Support Commands:
```bash
# Check database connection
mysql -u username -p -e "SELECT 1;"

# Validate CSV format
head -5 your_meta_titles.csv

# Check current meta titles
mysql -u username -p -e "SELECT COUNT(*) FROM destinations WHERE meta_title LIKE '%2025/26%';"
```

## 📝 Notes

- **Script includes confirmation prompt** - you must press Enter to proceed
- **All changes are logged** with before/after values
- **Errors are captured** and reported at the end
- **No changes to other destination data** - only meta_title and modified timestamp
- **Safe to run multiple times** - will only update changed values
