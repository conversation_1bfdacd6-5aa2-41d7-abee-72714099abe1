#!/bin/bash

# Here's a dynamic Bash script that achieves your requirements. It supports --files and --mode options, dynamically handles files, and provides clear logging.

# Script: deploy_manager.sh

# Get the current working directory (where the command is run from)
WORKING_DIR="$(pwd)"

# Default docroot path
DEFAULT_DOCROOT="/var/app/current"

# Directory for backup files (in the working directory)
BACKUP_DIR="$WORKING_DIR/_archive"

# Ensure the backup directory exists
mkdir -p "$BACKUP_DIR"

# Function to display usage
usage() {
    echo
    echo -e "\033[1mUsage:\033[0m $0 --files \"file1 file2 ...\" --mode deploy|rollback [--docroot path] [--dry-run] [--force] [--no-cache-clear]"
    echo
    echo "  -f, --files        Space-separated list of file paths to process"
    echo "  -m, --mode         Mode to run: deploy or rollback"
    echo "  -d, --docroot      Target document root (default: /var/app/current)"
    echo "  -n, --dry-run      Show what would happen without making changes"
    echo "  --force            Continue even if ownership change fails"
    echo "  --no-cache-clear   Skip clearing the application cache after deployment"
    echo ""
    echo -e "\033[1mExamples:\033[0m"
    echo
    echo "  # Preview deployment of a file without making changes:"
    echo "  $0 --files \"app/views/layouts/prod/default.ctp\" --mode deploy --dry-run"
    echo ""
    exit 1
}

# Parse arguments
FILES=()
MODE=""
DOCROOT="$DEFAULT_DOCROOT"
DRY_RUN=false
FORCE=false
CLEAR_CACHE=true
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--files)
            IFS=' ' read -r -a FILES <<< "$2"
            shift 2
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -d|--docroot)
            DOCROOT="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --no-cache-clear)
            CLEAR_CACHE=false
            shift
            ;;
        *)
            usage
            ;;
    esac
done

# Validate arguments
if [[ -z "$FILES" || -z "$MODE" ]]; then
    usage
fi

if [[ "$MODE" != "deploy" && "$MODE" != "rollback" ]]; then
    echo "Error: Invalid mode '$MODE'. Use 'deploy' or 'rollback'."
    exit 1
fi

# Expand docroot path (handle ~/ and other expansions)
DOCROOT=$(eval echo "$DOCROOT")

# Print operation header with reverse video
MODE_DISPLAY="$(tr '[:lower:]' '[:upper:]' <<< ${MODE:0:1})${MODE:1}"
if [ "$DRY_RUN" = true ]; then
    printf "\n\033[7m DRY RUN: %sing... \033[0m\n" "$MODE_DISPLAY"
else
    printf "\n\033[7m %sing... \033[0m\n" "$MODE_DISPLAY"
fi

# Get the list of files in the working directory
SCRIPT_NAME=$(basename "$0")
AVAILABLE_FILES=$(ls -p "$WORKING_DIR" | grep -v "/$")

# Get total number of files
TOTAL_FILES=${#FILES[@]}
CURRENT_FILE=1

# Perform operations on each file
for FILE in "${FILES[@]}"; do
    BASENAME=$(basename "$FILE")
    
    if ! [[ "$AVAILABLE_FILES" =~ "$BASENAME" ]]; then
        echo "Skipping: $BASENAME not found in $WORKING_DIR"
        continue
    fi

    # Print file counter header
    printf "\n\033[1m[%d/%d]: %s\033[0m\n\n" "$CURRENT_FILE" "$TOTAL_FILES" "$BASENAME"

    # Change ownership of the local file before any operations
    if [ "$DRY_RUN" = false ]; then
        if ! sudo chown webapp:webapp "$WORKING_DIR/$BASENAME" 2>/dev/null; then
            if [ "$FORCE" = true ]; then
                echo "Warning: Failed to change ownership, continuing anyway..."
                echo
            else
                echo "Error: Failed to change ownership. Please check if group 'webapp' exists."
                echo "Use --force to continue anyway."
                exit 1
            fi
        else
            echo "Changed ownership"
            echo
        fi
    fi

    case $MODE in
        deploy)
            # Backup the existing file if it exists
            if [ -f "$DOCROOT/$FILE" ]; then
                if [ "$DRY_RUN" = false ]; then
                    sudo cp "$DOCROOT/$FILE" "$BACKUP_DIR/$BASENAME"
                fi
                printf "Backup created at:\n%s\n" "$BACKUP_DIR/$BASENAME"
                echo
            else
                printf "No existing file at:\n%s\n" "$DOCROOT/$FILE"
                echo
            fi

            # Deploy the new file
            if [ "$DRY_RUN" = false ]; then
                sudo cp "$WORKING_DIR/$BASENAME" "$DOCROOT/$FILE"
            fi
            printf "Deployed to:\n%s\n" "$DOCROOT/$FILE"
            echo
            ;;
        rollback)
            # Rollback to the backup file
            if [[ -f "$BACKUP_DIR/$BASENAME" ]]; then
                if [ "$DRY_RUN" = false ]; then
                    sudo cp "$BACKUP_DIR/$BASENAME" "$DOCROOT/$FILE"
                fi
                printf "Rolled back to:\n%s\n" "$DOCROOT/$FILE"
                echo
            else
                printf "No backup found at:\n%s\n" "$BACKUP_DIR/$BASENAME"
                echo
            fi
            ;;
    esac

    # Print separator if not the last file
    if [ $CURRENT_FILE -lt $TOTAL_FILES ]; then
        echo "--"
        echo
    fi

    ((CURRENT_FILE++))
done

# Clear cache after deployment if enabled
if { [ "$MODE" = "deploy" ] || [ "$MODE" = "rollback" ]; } && [ "$CLEAR_CACHE" = true ]; then
    if [ "$DRY_RUN" = false ]; then
        printf "\n\033[1mClearing application cache...\033[0m\n"
        if sudo find "$DOCROOT/app/tmp/cache/" -type f -delete; then
            echo "Cache cleared successfully."
        else
            echo "Warning: Failed to clear cache."
        fi
    else
        printf "\n\033[1mDRY RUN: Would clear application cache\033[0m\n"
        echo "Would run: sudo find $DOCROOT/app/tmp/cache/ -type f -delete"
    fi
    echo
fi

echo -e "\033[1mOperation '$MODE' completed.\033[0m"
echo

# How to Use:

# 	1.	Navigate to the folder containing the files to deploy
# 	2.	Run the script (from any location) with the required options
# 	•	For deployment:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode deploy


# 	•	For rollback:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode rollback


# 	•	To deploy without clearing cache:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp" --mode deploy --no-cache-clear


# Key Features:

# 	1.	Location Independent: Script can be located anywhere, operates on files in current working directory
# 	2.	Dynamic File Handling: Matches filenames from the provided --files argument with files in working directory
# 	3.	Error Handling: Skips files not present in the working directory or missing backups
# 	4.	Mode Validation: Ensures valid modes (deploy or rollback) are used
# 	5.	Backup Management: Creates backups in ./_archive (in working directory) during deployment
# 	6.	Clear Logs: Prints actions for transparency with full paths
# 	7.	Cache Clearing: Automatically clears application cache after deployment (can be disabled)
# 
# This script adapts to your specific requirements and ensures safe, repeatable file deployments and rollbacks.
