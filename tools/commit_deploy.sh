#!/bin/bash

# Commit Deploy Tool
# Copies files changed between two commits to /DEPLOY directory
# Outputs file list to terminal and clipboard

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
    echo
    echo -e "${BLUE}Commit Deploy Tool${NC}"
    echo "=================="
    echo
    echo -e "${YELLOW}Usage:${NC} $0 <start_commit> <end_commit> [options]"
    echo
    echo -e "${YELLOW}Arguments:${NC}"
    echo "  start_commit    Starting commit hash (inclusive)"
    echo "  end_commit      Ending commit hash (inclusive)"
    echo "                  Use 'HEAD' for latest commit"
    echo
    echo -e "${YELLOW}Options:${NC}"
    echo "  -d, --deploy-dir DIR    Target directory (default: DEPLOY)"
    echo "  -n, --dry-run          Show what would be copied without doing it"
    echo "  -v, --verbose          Show detailed output"
    echo "  -c, --no-clipboard     Don't copy file list to clipboard"
    echo "  -f, --force            Skip duplicate filename check (dangerous)"
    echo "  -p, --preserve-paths   Preserve directory structure (default: flat)"
    echo "  -h, --help             Show this help message"
    echo
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0 abc123 def456                    # Copy files to flat structure in DEPLOY"
    echo "  $0 abc123 HEAD                      # Copy files changed from abc123 to latest"
    echo "  $0 abc123 def456 --dry-run          # Preview what would be copied"
    echo "  $0 abc123 def456 -d STAGING         # Copy to STAGING directory instead"
    echo "  $0 abc123 def456 --preserve-paths   # Keep original directory structure"
    echo
    echo -e "${YELLOW}Note:${NC} Both start_commit and end_commit are inclusive"
    echo "      Files are copied to a flat structure by default"
    echo "      Use --preserve-paths to maintain directory structure"
    echo
    exit 1
}

# Default values
DEPLOY_DIR="DEPLOY"
DRY_RUN=false
VERBOSE=false
USE_CLIPBOARD=true
FORCE=false
PRESERVE_PATHS=false

# Parse arguments
START_COMMIT=""
END_COMMIT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--deploy-dir)
            DEPLOY_DIR="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--no-clipboard)
            USE_CLIPBOARD=false
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -p|--preserve-paths)
            PRESERVE_PATHS=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        -*)
            echo -e "${RED}Error: Unknown option $1${NC}"
            usage
            ;;
        *)
            if [[ -z "$START_COMMIT" ]]; then
                START_COMMIT="$1"
            elif [[ -z "$END_COMMIT" ]]; then
                END_COMMIT="$1"
            else
                echo -e "${RED}Error: Too many arguments${NC}"
                usage
            fi
            shift
            ;;
    esac
done

# Validate required arguments
if [[ -z "$START_COMMIT" || -z "$END_COMMIT" ]]; then
    echo -e "${RED}Error: Both start_commit and end_commit are required${NC}"
    usage
fi

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}Error: Not in a git repository${NC}"
    exit 1
fi

# Validate commit hashes
if ! git rev-parse --verify "$START_COMMIT" > /dev/null 2>&1; then
    echo -e "${RED}Error: Invalid start commit: $START_COMMIT${NC}"
    exit 1
fi

if ! git rev-parse --verify "$END_COMMIT" > /dev/null 2>&1; then
    echo -e "${RED}Error: Invalid end commit: $END_COMMIT${NC}"
    exit 1
fi

# Get the parent of the start commit to make the range truly inclusive
START_PARENT=$(git rev-parse "$START_COMMIT^" 2>/dev/null)
if [[ $? -ne 0 ]]; then
    # If start commit has no parent (root commit), use empty tree
    START_PARENT="4b825dc642cb6eb9a060e54bf8d69288fbee4904"
    echo -e "${YELLOW}Note: Start commit appears to be root commit, including all files${NC}"
fi

# Get the list of changed files (inclusive range)
echo -e "${BLUE}Getting changed files from $START_COMMIT to $END_COMMIT (inclusive)...${NC}"

# Use git diff from parent of start commit to end commit to get inclusive range
CHANGED_FILES=$(git diff --name-only "$START_PARENT" "$END_COMMIT")

if [[ -z "$CHANGED_FILES" ]]; then
    echo -e "${YELLOW}No files changed in the specified commit range${NC}"
    exit 0
fi

# Count files
FILE_COUNT=$(echo "$CHANGED_FILES" | wc -l | tr -d ' ')

echo -e "${GREEN}Found $FILE_COUNT changed files${NC}"

# Check for duplicate filenames (unless forced or preserving paths)
if [[ "$FORCE" == false ]]; then
    if [[ "$PRESERVE_PATHS" == false ]]; then
        echo -e "${BLUE}Checking for duplicate filenames (flat structure)...${NC}"
        DUPLICATE_CHECK=$(echo "$CHANGED_FILES" | xargs -I {} basename {} | sort | uniq -d)

        if [[ -n "$DUPLICATE_CHECK" ]]; then
            echo -e "${RED}❌ ERROR: Duplicate filenames detected!${NC}"
            echo
            echo -e "${RED}The following filenames appear multiple times:${NC}"
            while IFS= read -r duplicate_name; do
                if [[ -n "$duplicate_name" ]]; then
                    echo -e "${RED}  • $duplicate_name${NC}"
                    echo -e "${YELLOW}    Found in:${NC}"
                    echo "$CHANGED_FILES" | while IFS= read -r file; do
                        if [[ "$(basename "$file")" == "$duplicate_name" ]]; then
                            echo -e "${YELLOW}      - $file${NC}"
                        fi
                    done
                fi
            done <<< "$DUPLICATE_CHECK"
            echo
            echo -e "${RED}Deployment aborted to prevent file conflicts in flat structure.${NC}"
            echo -e "${YELLOW}Options to resolve:${NC}"
            echo -e "${YELLOW}  • Use --preserve-paths to maintain directory structure${NC}"
            echo -e "${YELLOW}  • Rename conflicting files before deployment${NC}"
            echo -e "${YELLOW}  • Use --force to override this check (dangerous)${NC}"
            exit 1
        fi

        echo -e "${GREEN}✅ No duplicate filenames found${NC}"
    else
        echo -e "${BLUE}Using preserved paths - duplicate filename check skipped${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Skipping duplicate filename check (--force enabled)${NC}"
fi
echo

if [[ "$VERBOSE" == true ]]; then
    echo -e "${YELLOW}Changed files:${NC}"
    echo "$CHANGED_FILES" | sed 's/^/  /'
    echo
fi

# Create deploy directory if not in dry-run mode
if [[ "$DRY_RUN" == false ]]; then
    if [[ ! -d "$DEPLOY_DIR" ]]; then
        mkdir -p "$DEPLOY_DIR"
        echo -e "${GREEN}Created directory: $DEPLOY_DIR${NC}"
    else
        echo -e "${YELLOW}Cleaning existing directory: $DEPLOY_DIR${NC}"
        rm -rf "$DEPLOY_DIR"/*
    fi
fi

# Copy files
if [[ "$PRESERVE_PATHS" == true ]]; then
    echo -e "${BLUE}Copying files to $DEPLOY_DIR (preserving directory structure)...${NC}"
else
    echo -e "${BLUE}Copying files to $DEPLOY_DIR (flat structure)...${NC}"
fi
echo

COPIED_COUNT=0
FAILED_COUNT=0
FAILED_FILES=()

while IFS= read -r file; do
    if [[ -z "$file" ]]; then
        continue
    fi

    if [[ ! -f "$file" ]]; then
        echo -e "${RED}  ❌ $file (file not found)${NC}"
        FAILED_COUNT=$((FAILED_COUNT + 1))
        FAILED_FILES+=("$file")
        continue
    fi

    # Determine target path based on preserve_paths setting
    if [[ "$PRESERVE_PATHS" == true ]]; then
        target_file="$DEPLOY_DIR/$file"
        display_target="$file"
    else
        target_file="$DEPLOY_DIR/$(basename "$file")"
        display_target="$(basename "$file")"
    fi

    if [[ "$DRY_RUN" == true ]]; then
        echo -e "${YELLOW}  📋 $file → $display_target (would copy)${NC}"
        COPIED_COUNT=$((COPIED_COUNT + 1))
    else
        # Create target directory if preserving paths
        if [[ "$PRESERVE_PATHS" == true ]]; then
            target_dir="$DEPLOY_DIR/$(dirname "$file")"
            if ! mkdir -p "$target_dir" 2>/dev/null; then
                echo -e "${RED}  ❌ $file (failed to create directory)${NC}"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                FAILED_FILES+=("$file")
                continue
            fi
        fi

        # Copy the file
        if cp "$file" "$target_file" 2>/dev/null; then
            echo -e "${GREEN}  ✅ $file → $display_target${NC}"
            COPIED_COUNT=$((COPIED_COUNT + 1))
        else
            echo -e "${RED}  ❌ $file (copy failed)${NC}"
            FAILED_COUNT=$((FAILED_COUNT + 1))
            FAILED_FILES+=("$file")
        fi
    fi
done <<< "$CHANGED_FILES"

# Summary
echo
echo -e "${BLUE}Summary:${NC}"
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}  Would copy: $COPIED_COUNT files${NC}"
else
    echo -e "${GREEN}  Successfully copied: $COPIED_COUNT files${NC}"
fi

if [[ $FAILED_COUNT -gt 0 ]]; then
    echo -e "${RED}  Failed: $FAILED_COUNT files${NC}"
    if [[ "$VERBOSE" == true ]]; then
        echo -e "${RED}  Failed files:${NC}"
        for failed_file in "${FAILED_FILES[@]}"; do
            echo -e "${RED}    - $failed_file${NC}"
        done
    fi
fi

# Copy to clipboard
if [[ "$USE_CLIPBOARD" == true && $COPIED_COUNT -gt 0 ]]; then
    if command -v pbcopy > /dev/null 2>&1; then
        # macOS
        echo "$CHANGED_FILES" | tr '\n' ' ' | pbcopy
        echo -e "${GREEN}  File list copied to clipboard${NC}"
    elif command -v xclip > /dev/null 2>&1; then
        # Linux with xclip
        echo "$CHANGED_FILES" | tr '\n' ' ' | xclip -selection clipboard
        echo -e "${GREEN}  File list copied to clipboard${NC}"
    elif command -v xsel > /dev/null 2>&1; then
        # Linux with xsel
        echo "$CHANGED_FILES" | tr '\n' ' ' | xsel --clipboard --input
        echo -e "${GREEN}  File list copied to clipboard${NC}"
    else
        echo -e "${YELLOW}  Clipboard not available (install pbcopy, xclip, or xsel)${NC}"
    fi
fi

echo
echo -e "${BLUE}Operation complete!${NC}"

if [[ "$DRY_RUN" == false && $COPIED_COUNT -gt 0 ]]; then
    echo -e "${GREEN}Files are ready in: $DEPLOY_DIR${NC}"
fi
