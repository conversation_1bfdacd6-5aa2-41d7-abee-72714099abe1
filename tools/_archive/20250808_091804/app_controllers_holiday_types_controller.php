<?php

class HolidayTypesController extends AppController {

  var $name = 'HolidayTypes';
  var $components = array('Navigation', 'Section');

  function index() {

    $this->criticalCss = 'holidays';

    $holidayTypes = $this->_findHolidayTypes();

    $this->set(compact('holidayTypes'));

  }

  function view() {

    $this->criticalCss = 'holidaysView';

    //This controller uses the section component. The component already
    //returns the holiday type data and calls a 404 if not found. So in
    //theory it should never reach here if the data is empty, but just
    //in case, here's another check
    if (empty($this->sectionData['HolidayType'])) {
      $this->cakeError('error404');
    }

    $holidayType = $this->sectionData;

    $related = $this->_findDestinations($this->sectionId);

    $this->set(compact('holidayType', 'related'));
  }

  /**
   * Returns the full list of holiday types
   */
  protected function _findHolidayTypes() {
    $holidays = function() {
        return $this->HolidayType->find('all', array(
            'conditions' => array('HolidayType.published' => 1),
            'order' => array('HolidayType.order ASC'),
            'recursive' => 1
        ));
    };

    return $this->cacher('holiday_types_with_images', $holidays);
  }

  /**
   * Returns a holiday types related destinations
   */
  protected function _findDestinations($sectionId) {
    $destinations = function() use ($sectionId) {
      return ClassRegistry::init('Destination')->getDestinationsOnHolidayType($sectionId);
    };

    return $this->cacher(implode('_', array(
      'holidaytype', $sectionId, 'destinations'
    )), $destinations);
  }

  function webadmin_edit($id = null) {
    error_log("[Navigation] Starting holiday type edit for ID: " . $id);

    parent::webadmin_edit($id);

    if ($this->data) {
        error_log("[Navigation] Clearing cache due to holiday type update");
        Cache::delete('main_navigation', 'navigation');
    }

    // ... rest of existing code
  }
}

?>
