<?php
$cssPath = 'css/build/style.css';
$cssTimestamp = filemtime(WWW_ROOT . $cssPath);

$this->set('additionalStyles', '<link rel="stylesheet" href="/'. $cssPath .'?ver=' . $cssTimestamp . '">'); ?>

<section id="app" class="container travel-plan js-page-content-body">
    <div>
        <header class="travel-plan__header">
            <h1 class="headline-1">Your Travel Plan</h1>

            <div class="typography trim travel-plan__intro">

            </div>
        </header>
        <?php if ($session->check('Message.flash.params.hide_form')): ?>
            <?php
                $javascript->codeBlock(
                "Event.observe(window, 'load', function(e){try{window.ga('send','pageview','/quote-request-submitted');}catch(e){}});",
                array(
                    'inline' => false
                )
                );
                $javascript->codeBlock(
                "Event.observe(window, 'load', function(e){try{window.fbq('track','Lead');}catch(e){}});",
                array(
                    'inline' => false
                )
                );
            ?>

            <div class="content-block">
                <h2>Thank you!</h2>

                <h3>We will be in touch.</h3>
            </div>

            <?php if($session->check('Message.flash')) { $session->flash(); } ?>

            <script type="text/javascript">
                /* <![CDATA[ */
                var google_conversion_id = 1030719956;
                var google_conversion_language = "en";
                var google_conversion_format = "1";
                var google_conversion_color = "ffffff";
                var google_conversion_label = "_MOACOzopAUQ1JO-6wM";
                var google_conversion_value = 0;
            </script>

            <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js"></script>

            <noscript>
                <div style="display:inline;">
                <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/1030719956/?value=0&amp;label=_MOACOzopAUQ1JO-6wM&amp;guid=ON&amp;script=0"/>
                </div>
            </noscript>
        <?php else: ?>
            <?php
                echo $webAdmin->formCreate('TravelPlan', array(
                    'url' => array('action' => 'add_lite', '#' => 'flashMessage'),
                    'class' => 'js-travel-plan-form',
                    'type' => 'post'
                ));
            ?>
            <?php
                // Set default contact method
                echo $form->input('contact_method', ['type' => 'hidden', 'value' => 'Consultation Call']);
            ?>

            <div class="card card--text-center ready-to-plan-mobile">
                <div class="card__content">
                    <?php echo $image->image('site/icons/call.svg', [
                        'class' => 'card__icon'
                    ]); ?>

                    <span class="card__title">Ready to plan your holiday?</span>

                    <p>Chat to an expert today on <span class="nowrap">0800 316 3012</span> or email us at <a href="mailto:<EMAIL>" class="nowrap"><EMAIL></a>.<br><br>If you'd prefer to come in and see one of our team, please phone or email to make an appointment. Alternatively, fill in our enquiry form and we'll be in touch.</p>
                </div>
            </div>

            <div class="form-card">
                <div class="form-card__header">
                    <h2 class="form-card__title">Plan Details</h2>

                    <span>Required <span class="form-card__star">*</span></span>
                </div>

                <div class="form-card__body">
                    <div class="form">
                        <?php $session->flash(); ?>

                        <travel-plan-destination
                            :data="<?php echo $app->toJson($form->data['TravelPlan'] ?? null)?>"
                            :errors="<?php echo $app->toJson($validationErrors)?>"
                            :countries="<?php echo $app->toJson($destinationCountries) ?>"
                            :regions="<?php echo $app->toJson($destinationRegions) ?>">
                            <?php
                            echo $form->input('destination_country', ['div' => 'input select input--half required', 'label' => ['class' => 'label']]);
                            echo $form->input('destination_region', ['div' => 'input select input--half required', 'label' => ['class' => 'label']]); ?>
                        </travel-plan-destination>


                        <fieldset class="input number required">
                            <legend class="label">Number of travellers</legend>

                            <div class="input-group travel-plan__numbers">
                                <div class="person-counter">
                                    <div class="person-counter__text">
                                        <label class="person-counter__label" for="numAdults">Adults</label>
                                        <span class="person-counter__note">Aged 16+</span>
                                    </div>
                                    <counter :value="<?php echo $form->data['TravelPlan']['num_adults'] ?? 1 ?>" :min="1" name="data[TravelPlan][num_adults]" id="numAdults">
                                        <?php echo $form->input('num_adults', ['div' => 'input text required']); ?>
                                    </counter>
                                </div>
                                <div class="person-counter">
                                    <div class="person-counter__text">
                                        <label class="person-counter__label" for="numChildren">Children</label>
                                        <span class="person-counter__note">Aged 0-15</span>
                                    </div>
                                    <counter :value="<?php echo $form->data['TravelPlan']['num_children'] ?? 0 ?>" name="data[TravelPlan][num_children]" id="numChildren">
                                        <?php echo $form->input('num_children', ['div' => 'input text required']); ?>
                                    </counter>
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="input required">
                            <?php $dateError = isset($validationErrors['travel_date']); ?>
                            <legend class="label">Date</legend>
                            <div class="form__row">
                                <?php
                                echo $form->select('travel_date_month', $months, null, ['class' => 'input--half ' . ($dateError ? 'error' : '')], 'Month');

                                echo $form->select('travel_date_year', $years, null, ['class' => 'input--half ' . ($dateError ? 'error' : '')], 'Year');
                                ?>
                            </div>

                            <?php if ($dateError) { ?>
                                <div class="error-message"><?php echo $validationErrors['travel_date']; ?></div>
                            <?php } ?>
                        </fieldset>

                        <range-slider
                            class="travel-plan__budget"
                            label="Please let us know what your budget is per person"
                            :min-value="<?php echo $form->data['TravelPlan']['min_budget'] ?? 2500?>"
                            :max-value="<?php echo $form->data['TravelPlan']['max_budget'] ?? 10000?>"
                            :required="true"
                            :min="2500"
                            :max="10000"
                            :step="500"
                            :add-plus="true"
                            from-name="data[TravelPlan][min_budget]"
                            to-name="data[TravelPlan][max_budget]">
                            <div class="input select required input--half">
                                <label class="label" for="TravelPlanMinBudget">Min Budget</label>
                                <?php echo $form->select('min_budget', $budgets, null, [], false); ?>
                            </div>

                            <div class="input select required input--half">
                                <label class="label" for="TravelPlanMaxBudget">Max Budget</label>
                                <?php echo $form->select('max_budget', $budgets, null, [], false); ?>
                            </div>
                        </range-slider>
                        <?php
                        echo $form->input('other_info', ['div' => 'input text required', 'label' => ['text' => 'Please share your ideas', 'class' => 'label'], 'placeholder' => 'We\'ll tailor your holiday to your individual plans and wishes...']);
                        ?>
                    </div>
                </div>
            </div>

            <div class="form-card">
                <div class="form-card__header">
                    <h2 class="form-card__title">Your Contact Details</h2>
                    <span>Required <span class="form-card__star">*</span></span>
                </div>
                <div class="form-card__body">
                    <div class="form">
                        <span>Please let us know the best way/time to be in touch...</span>
                        <?php
                        echo $form->input('first_name', ['div' => 'input text input--half required', 'label' => ['text' => 'First name', 'class' => 'label']]);
                        echo $form->input('last_name', ['div' => 'input text input--half required', 'label' => ['text' => 'Last name', 'class' => 'label']]);
                        echo $form->input('email_address', ['div' => 'input text input--half required', 'label' => ['text' => 'Email address', 'class' => 'label']]);
                        echo $form->input('telephone_number', ['div' => 'input text input--half required', 'label' => ['text' => 'Telephone number', 'class' => 'label']]);
                        echo $form->input('follow_up_method', ['div' => 'input checkbox input--half checkbox-spaced required', 'label' => ['text' => 'How should we follow up?', 'class' => 'label'], 'type' => 'select', 'multiple' => 'checkbox', 'options' => ['Phone' => 'Phone', 'Email' => 'Email', 'Text' => 'Text'], 'selected' => ['Phone', 'Email', 'Text']]);
                        echo $form->input('preferred_time', ['div' => 'input select input--half', 'label' => ['text' => 'Preferred time to call', 'class' => 'label'], 'empty' => 'Please select']);
                        echo $form->input('email_consent', ['label' => 'Please sign me up for Bon Voyage e-news – a weekly newsletter of ideas and inspiration for your North America holiday', 'div' => 'switch']);
                        echo $form->input('utm', ['type'=>'hidden','value' => '']);
                        ?>
                    </div>
                    <div class="form-card__footer trim">
                        <button type="submit" class="button button--with-arrow button--block">Submit enquiry</button>

                        <p class="form-card__subnote">After submitting the form, we will match you with one of our experts and be in contact.</p>
                    </div>
                </div>
            </div>

            <?php echo $form->end(); ?>
        <?php endif ?>
    </div>
    <div class="travel-plan__sidebar">
        <div class="card card--text-center ready-to-plan-desktop">
            <div class="card__content">
                <?php echo $image->image('site/icons/call.svg', [
                    'class' => 'card__icon'
                ]); ?>

                <span class="card__title">Ready to plan your holiday?</span>

                <p>Chat to an expert today on <span class="nowrap">0800 316 3012</span> or email us at <a href="mailto:<EMAIL>" class="nowrap"><EMAIL></a>.<br><br>If you'd prefer to come in and see one of our team, please phone or email to make an appointment. Alternatively, fill in our enquiry form and we'll be in touch.</p>
            </div>
        </div>

        <div class="card opening-hours">
            <div class="card__content">
                <?php echo $image->image('site/icons/clock.svg', [
                    'class' => 'card__icon'
                ]); ?>

                <span class="card__title">Office Hours</span>

                <div class="opening-hours__row">
                    <span class="opening-hours__day">Mon-Thurs</span>
                    <span>9:00 - 18:00</span>
                </div>
                <div class="opening-hours__row">
                    <span class="opening-hours__day">Fri</span>
                    <span>9:00 - 17:30</span>
                </div>
                <div class="opening-hours__row">
                    <span class="opening-hours__day">Sat</span>
                    <span>9:00 - 15:00</span>
                </div>
            </div>
        </div>

        <div class="card card--text-center">
            <div class="card__content">
                <?php echo $image->image('site/icons/location.svg', [
                    'class' => 'card__icon'
                ]); ?>

                <span class="card__title">Where we are</span>

                <div>
                    <address class="travel-plan__address trim">
                        <p>16-18 Bellevue Road,<br>Southampton, SO15 2AY</p>
                    </address>
                </div>
            </div>
        </div>

        <div class="card feefo-card card--text-center">
            <div class="card__content">
                <?php echo $image->image('site/icons/testimonials.svg', [
                    'class' => 'card__icon'
                ]); ?>
                <span class="card__title">What our customers say</span>
                <a href="https://www.feefo.com/en/reviews/bon-voyage" target="_blank" rel="noopener">
                    <img src="/proxy/feefo/?resource=<?php echo urlencode('feefo/feefologo.jsp?logon=www.bon-voyage.co.uk&amp;template=n150x45.png') ?>" alt="Feefo Reviews" />
                    <br>
                    <p class="button button--with-arrow feefo-link">Read all reviews</p>
                </a>
                <a href="https://bon-voyage.ddev.site/page/testimonials" class="button button--with-arrow">Bon Voyage Customer testimonials</a>
            </div>
            <?php echo $image->image('site/img/make_an_enquiry_sm.jpeg', [
                'class' => 'card__image'
            ]); ?>
        </div>
    </div>
</section>
<?php echo $this->element('chrome/usp_footer'); ?>
