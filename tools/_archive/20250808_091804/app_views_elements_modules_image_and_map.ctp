<?php
if (isset($hideTheImage) && empty($mapData)) {
    return;
}
?>
<div class="image-and-map image-and-map--<?php echo $modifier ?>">
    <?php if (empty($mapData) && (!isset($showMap) || $showMap !== true)): ?>
        <div class="image-and-map__image image-and-map__image--full-image">
            <?php
            echo $image->image($theImage, array('version' => 'crop1000x479'));
            ?>
        </div>
    <?php else: ?>
        <?php if (empty($hideTheImage)): ?>
            <div class="image-and-map__image image-and-map__image--full-image">
                <?php
                echo $image->image($theImage, array('version' => 'crop1000x479'));
                ?>
            </div>
        <?php endif ?>

        <div class="image-and-map__map-wrapper <?php if (!empty($hideTheImage)) { echo 'image-and-map__map-wrapper--full-width'; } ?> <?php if (!isset($showMap) || $showMap !== true) { echo 'image-and-map__map-wrapper--fixed'; } ?>">
            <div class="image-and-map__map-wrapper__map" id="image-and-map__map"></div>
            <button class="image-and-map__hide" type="button">Close</button>
            <noscript>
                <?php
                echo $html->image(
                    implode('', array(
                        '//maps.googleapis.com/maps/api/staticmap?center=',
                        $mapData['map_latitude'],
                        ',',
                        $mapData['map_longitude'],
                        '&amp;zoom=',
                        $mapData['zoom_level'],
                        '&amp;size=355x340&amp;maptype=terrain'
                    )),
                    array('width' => '355', 'height' => '340'
                ));
                ?>
            </noscript>
        </div>
    <?php endif ?>
</div>

<?php

if (empty($mapData)) {
    return;
}

$mapStr = $app->map_js_config('image-and-map__map', $mapData);

echo $javascript->codeBlock($mapStr, array('inline' => false));
