<?php if (empty($contentBlocks)) { return; } ?>

<div class="content-blocks <?php if (isset($modifier)) { echo 'content-blocks--' . $modifier; } ?>">
  <?php

  $count = count($contentBlocks);

  foreach ($contentBlocks as $i => $contentBlock) {

    $contentBlockImage = '';
    $contentBlockVideo = '';

    if (isset($contentBlock['ContentBlock']['Image'])) {
      $contentBlockImage = $contentBlock['ContentBlock']['Image'];
    } elseif (isset($contentBlock['Image'])) {
      $contentBlockImage = $contentBlock['Image'];
    }

    if (isset($contentBlock['ContentBlock'])) {
      $contentBlock = $contentBlock['ContentBlock'];
    }

    // generate image mark-up
    $contentBlockImage = $image->image($contentBlockImage, array(
      'divClass' => 'content-block__image content-block__image--' . $contentBlock['alignment'],
      'version' => 'crop370x370',
      'hideSizes' => true
    ));

    // generate video mark-up
    if (empty($contentBlockImage) && !empty($contentBlock['youtube_video_id'])) {
      $contentBlockVideo = $image->video($contentBlock['youtube_video_id'], array(
        'class' => 'content-block__video content-block__video--'.$contentBlock['alignment']
      ));
    }

    // get the text mark-up
    $contentBlockText = $app->echoIf($contentBlock['content']);

    $contentBlockLink = null;

    // generate link mark-up
    if (!empty($contentBlock['link']) && !empty($contentBlock['link_text'])) {
      $contentBlockLink = '<p class="content-block__link">'.$html->link($contentBlock['link_text'], $contentBlock['link']).'</p>';
    }

    // get out if everything is empty
    if (!$contentBlockImage && !$contentBlockText && !$contentBlockLink && !$contentBlockVideo) {
      continue;
    }

    // generate css classes
    $cssClass = $app->cssClasses(array(
      'content-block',
      'content-block--' . $image->hasImage($contentBlockImage),
      (!empty($contentBlockImage)) ? 'content-block--'.$contentBlock['alignment'] : ''
    ));

    // output everything
    echo  '<div class="'.$cssClass.'">'.
            $contentBlockImage.
            '<div class="content-block__text">'.
              $contentBlockText.
              $contentBlockLink.
            '</div>'.
            $contentBlockVideo.
          '</div>';
  }

  ?>
</div>
