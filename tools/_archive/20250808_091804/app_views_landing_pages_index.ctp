<?php
  echo $this->element('modules/page_content_header', array(
    'header'   => 'Campaigns'
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      <div class="content-blocks content-blocks--landing-pages">
        <?php foreach ($landingPages as $l): ?>
          <div class="content-block content-block--landing-pages">
            <div class="content-block__text">
              <h2>
                <?php
                  echo $html->link($l['LandingPage']['name'], array(
                    'controller'        => 'landing_pages',
                    'action'            => 'view',
                    'landing_page_slug' => $l['LandingPage']['slug'],
                    'section'           => 'landing_pages'
                  ));
                ?>
              </h2>

              <?php if (!empty($l['MainImage']['id'])): ?>
                <div class="content-block__image content-block__image--left">
                  <?php
                  echo $image->image($l['MainImage'], array(
                      'version' => 'crop370x370',
                      'hideSizes' => true
                  ));
                  ?>
                </div>
              <?php endif ?>

              <?php echo $app->echoIf($l['LandingPage']['summary']); ?>

              <p class="content-block__link">
                <?php
                  echo $html->link('View <b>' . $l['LandingPage']['name'] . '</b>', array(
                    'controller'        => 'landing_pages',
                    'action'            => 'view',
                    'landing_page_slug' => $l['LandingPage']['slug'],
                    'section'           => 'landing_pages'
                  ), array(), false, false);
                ?>
              </p>
            </div>
          </div>
        <?php endforeach ?>
      </div>

      <?php
        $this->passedArgs = array_merge(
          $this->passedArgs,
          array(
            $sectionSlugParam => $sectionSlug,
            'section' => $section
          )
        );

        echo $this->element('site_pagination_links');
      ?>
    </div>

    <?php
      echo $this->element('sidebar', array(
        'modifier' => 'landing-pages'
      ));
    ?>
  </div>
</section>
