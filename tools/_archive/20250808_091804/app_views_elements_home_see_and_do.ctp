<?php
$linkArray = array(
    'controller' => 'activities',
    'action' => 'view',
    'section' => 'destinations'
);
?>

<div class="home-column home-column--see-and-do">
  <h2>What To See <br>And Do</h2>

  <div class="home-column__image-wrapper">
    <?php
      if ($image->hasImage($activities[0]['Activity']['Image'])) {
        $seeImg = $image->image($activities[0]['Activity']['Image'], array(
            'version' => array(
                array(
                    'version' => 'crop250x140',
                    'respond' => '250w'
                ),
                array(
                    'version' => 'crop500x240',
                    'respond' => '500w'
                )
            ),
            'hideSizes' => true,
            'noLightbox' => true
        ));

        echo $html->link($seeImg, array_merge(
            $linkArray,
            array(
                'destination_slug' => $activities[0]['Destination']['slug'],
                'activity_slug' => $activities[0]['Activity']['slug']
            )
        ), array('escape' => false));
      }
    ?>
  </div>

  <ul>
    <?php foreach ($activities as $a): ?>
      <li>
        <?php
        echo $html->link($a['Activity']['name'], array_merge(
            $linkArray,
            array(
                'destination_slug' => $a['Destination']['slug'],
                'activity_slug' => $a['Activity']['slug']
            )
        ));
        ?>
      </li>
    <?php endforeach ?>
  </ul>
</div>
