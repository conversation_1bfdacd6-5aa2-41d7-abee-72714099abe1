<div class="related-module related-module--activities">
    <h3>Things to see and do</h3>

    <div class="related-module__activities">
        <?php foreach ($destinationActivities as $d): ?>
            <?php $url = Router::url(array(
                $sectionSlugParam => $sectionSlug,
                'controller'      => 'activities',
                'action'          => 'view',
                'activity_slug'   => $d['Activity']['slug'],
                'section'         => $section
            )) ?>
            <div class="related-module__activity">
                <div class="related-module__image">
                    <?php
                        echo $html->link(
                            $image->image($d['Image'], array(
                                'version' => 'crop190x190',
                                'noLightbox' => true,
                            )),
                            $url,
                            array('escape' => false)
                        );
                    ?>
                </div>

                <div class="related-module__info">
                    <h4>
                        <?php
                        echo $html->link($d['Activity']['name'], $url);
                        ?>
                    </h4>

                    <?php echo $app->echoIf($d['Activity']['summary']); ?>

                    <p class="related-module__link">
                        <?php
                        echo $html->link('View <b>' . $d['Activity']['name'] . '</b>', $url, array('escape' => false));
                        ?>
                    </p>
                </div>
            </div>
        <?php endforeach ?>

        <p class="related-module__view-all">
            <?php
            echo $html->link(
                'View all ideas for <b>' . $destination['Destination']['name'] . '</b>',
                array(
                    $sectionSlugParam => $sectionSlug,
                    'controller'      => 'activities',
                    'action'          => 'index',
                    'section'         => $section
                ),
                array('escape' => false)
            );
            ?>
        </p>
    </div>
</div>
