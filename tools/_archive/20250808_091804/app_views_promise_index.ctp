<?php

// TODO
// Move to USA and Canada Travel Information section
// Get sidebar
// Check layout/template against existing pages
// Rename to The Bon Voyage Holiday Promise
// Correct responsive layout
// Test in Browserstack
// Add final images
// Fix gap on right side of page


  $testimonialParams = array();

  if (isset($sectionSlug)) {
    $testimonialParams[$sectionSlugParam] = $sectionSlug;
    $testimonialParams['section']         = $section;
  }
?>

<?php echo $this->element('section_header') ?>

<div class="content-blocks content-blocks--testimonials <?php if (isset($section)) { echo 'content-blocks--section'; } ?>">


  <?php
    $this->passedArgs = array_merge($this->passedArgs, $testimonialParams);

  ?>
</div>


<?php

if (empty($this->viewVars['breadcrumbs'])) {
    $breadcrumbs = array(array(
      'text' => "The Bon Voyage Holiday Promise",
      'url'  => $this->here
    ));
  }

  $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

//   $sectionHeader = "The Bon Voyage Holiday Promise";

  $hideTheImage = true;

  $this->set(compact('breadcrumbs', 'heroBannerImage', 'sectionHeader', 'hideTheImage'));

//   $this->_setMeta('Testimonials');

?>
<!-- <p>Intro text... unde omnis iste natus error sit voluptatem accusantium doloremque laudantium. totam rem aperiam eaque ipsa, quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt, explicabo.</p> -->

<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/service.jpg" alt="A cheerful customer service representative wearing a headset, ready to assist clients with their inquiries.">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/service.svg" alt="Hand holding Love Heart" class="promise-icon">
        <h3>Service</h3>
    </div>
    <p>Communications answered promptly by our friendly team of experts. Phone, e-mail, live chat, video call or drop in to our Southampton office. We are here to help before, during and after the holiday.</p>
</div>
</div>
<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/security.jpg" alt="A couple reviewing documents together, appearing focused and serious, likely discussing finances or important paperwork.">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/security.svg" alt="Shield with Check Mark" class="promise-icon">
        <h3>Security</h3>
    </div>
    <p>Your money and your holiday are safe when you book with Bon Voyage. We are bonded by the Civil Aviation Authority (ATOL 2913) and members of the Association of British Travel Agents (V6171).</p>
</div>
</div>
<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/experience.jpg" alt="A young woman with a backpack looking out over a cityscape, shielding her eyes from the sun as she explores">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/experience.svg" alt="Passport and Flight Tickets" class="promise-icon">
        <h3>Experience</h3>
    </div>
    <p>Our team know the USA and Canada with over 1,000 transatlantic trips of their own. We are specialists dealing only in holidays to North America since 1979.</p>
</div>
</div>
<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/price.jpg" alt="A person holding a credit card above a laptop, preparing for an online transaction.">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/price.svg" alt="Padlock with Dollar Sign" class="promise-icon">
        <h3>Price</h3>
    </div>
    <p>Once your booking is confirmed, and a deposit paid, the price is fixed. We guarantee the cost whether you book 14 days or 14 months before travel. You can spread the cost of your holiday with our Flexible Payment Plan.</p>
</div>
</div>
<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/affordability.jpg" alt="Two business professionals smiling and shaking hands, indicating a successful meeting or partnership.">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/affordability.svg" alt="Piggy Bank" class="promise-icon">
        <h3>Affordability</h3>
    </div>
    <p>The budget you set aside for your holiday will be put to the very best use. Our years of experience and unrivalled supplier relationships enable us to deliver optimum value.</p>
</div>
</div>
<div class="promise-row">
<img loading="lazy" class="promise-image" src="/img/site/content/promise/bv-app.jpg" alt="A hand holding a smartphone displaying a travel app - Bon Voyage - with travel information and a colorful image in the background.">
<div class="promise-text">
    <div class="promise-title">
        <img loading="lazy" src="/img/site/icons/promise/bv_app.svg" alt="Mobile Phone App" class="promise-icon">
        <h3>BV App</h3>
    </div>
    <p>The Bon Voyage app delivered to your phone before departure contains all your travel details, vouchers, tickets, live updates, points of interest, restaurants, museums, venues and landmarks.</p>
</div>
</div>

<?php

$accommodationsContent = null;
$activitiesContent = null;
$imagesContent = null;
$itinerariesContent = null;
$youtubeVideosContent = null;

echo $this->element('sidebar');

echo $this->element('section_footer')

?>
<style>
    .usp-footer { display: none; }
    .promise-title { display:flex; align-items: center;}
    .promise-title h3 { margin: 0; font-size: 26px; line-height: 30px; color: #6c4000;}

    @media screen and (max-width: 600px) {
        .promise-image { width: 100%; margin: 0 25px 25px 0}
        /* .promise-title { margin-bottom: 25px; } */
        .promise-row p { margin-bottom: 55px;}
        .promise-icon { margin: 0 15px 0 0}
    }
    @media screen and (min-width: 600px) {
        .promise-row { display:flex;}
        /* .promise-title { margin-bottom: 35px; } */
        .promise-image { width: 200px; margin: 0 25px 35px 0}
        .promise-icon { margin: 0 15px 0 0}
        .promise-text { display:flex; flex-direction: column;}

    }

  .page-content-body__content .promise-icon {
    width: 70px;
    height: 70px;
  }
</style>
