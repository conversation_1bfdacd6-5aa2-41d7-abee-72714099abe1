#!/bin/bash

# Test Scenarios for Deploy Manager
# Run this from the tools/deploy_test directory

echo "🧪 Deploy Manager Test Scenarios"
echo "================================="
echo

# Make sure we're in the right directory
if [ ! -f "deploy_manager.sh" ]; then
    echo "❌ Error: Please run this from the tools/deploy_test directory"
    exit 1
fi

echo "📁 Current test structure:"
echo "files/                    (source files to deploy)"
echo "├── app/config/database.php"
echo "├── webroot/css/main.css"
echo "├── plugins/auth/config.php"
echo "└── app/views/layouts/default.ctp"
echo
echo "app/                      (target deployment location)"
echo "├── config/database.php   (existing - will be overwritten)"
echo "├── webroot/css/main.css  (existing - will be overwritten)"
echo "├── controllers/          (empty directory)"
echo "└── views/layouts/        (empty directory)"
echo

echo "🎯 Test Scenarios:"
echo "1. Deploy files where there's no existing one (new file)"
echo "2. Deploy files where it's an overwrite (backup created)"
echo "3. Deploy files where target directory doesn't exist (should error)"
echo
echo "Press Enter to continue..."
read

echo
echo "=== TEST 1: Deploy to missing directory (should error) ==="
echo "Trying to deploy to plugins/auth/config.php (plugins directory doesn't exist)"
echo
echo "Command: ./deploy_manager.sh --files \"plugins/auth/config.php\" --mode deploy --nested --dry-run"
echo
./deploy_manager.sh --files "plugins/auth/config.php" --mode deploy --nested --dry-run

echo
echo "Press Enter to continue to Test 2..."
read

echo
echo "=== TEST 2: Deploy new file (no existing file) ==="
echo "Deploying to app/views/layouts/default.ctp (directory exists, file doesn't)"
echo
echo "Command: ./deploy_manager.sh --files \"app/views/layouts/default.ctp\" --mode deploy --nested"
echo
./deploy_manager.sh --files "app/views/layouts/default.ctp" --mode deploy --nested

echo
echo "📋 Let's check what was created:"
echo "File deployed:"
ls -la app/views/layouts/default.ctp 2>/dev/null && echo "✅ File exists" || echo "❌ File missing"
echo
echo "Backup created:"
ls -la _archive/*/app_views_layouts_default.ctp 2>/dev/null && echo "✅ No backup (new file)" || echo "✅ Correct - no backup needed for new file"
echo

echo "Press Enter to continue to Test 3..."
read

echo
echo "=== TEST 3: Deploy with overwrite (backup should be created) ==="
echo "Deploying to app/config/database.php (file exists - should create backup)"
echo
echo "Command: ./deploy_manager.sh --files \"app/config/database.php\" --mode deploy --nested"
echo
./deploy_manager.sh --files "app/config/database.php" --mode deploy --nested

echo
echo "📋 Let's check what was created:"
echo "File deployed:"
ls -la app/config/database.php 2>/dev/null && echo "✅ File exists" || echo "❌ File missing"
echo
echo "Backup created:"
ls -la _archive/*/app_config_database.php 2>/dev/null && echo "✅ Backup exists" || echo "❌ Backup missing"
echo
echo "Content comparison:"
echo "--- OLD VERSION (backup) ---"
head -5 _archive/*/app_config_database.php 2>/dev/null | grep "OLD VERSION" || echo "Backup file found"
echo
echo "--- NEW VERSION (deployed) ---"
head -5 app/config/database.php | grep "NEW VERSION" || echo "New file deployed"

echo
echo "Press Enter to continue to Test 4..."
read

echo
echo "=== TEST 4: Multiple file deployment ==="
echo "Deploying multiple files at once"
echo
echo "Command: ./deploy_manager.sh --files \"webroot/css/main.css app/config/database.php\" --mode deploy --nested"
echo
./deploy_manager.sh --files "webroot/css/main.css app/config/database.php" --mode deploy --nested

echo
echo "📋 Final directory structure:"
echo "app/"
find app -type f -exec ls -la {} \; 2>/dev/null

echo
echo "_archive/"
find _archive -type f -exec ls -la {} \; 2>/dev/null

echo
echo "🎉 All tests completed!"
echo
echo "💡 To test rollback, use the rollback command shown in the deployment output."
